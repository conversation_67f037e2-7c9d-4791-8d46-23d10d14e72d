#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour tester les nouveaux modèles SQLAlchemy de l'architecture RH complète
"""

import sys
import os
from datetime import date, datetime

# Ajouter le répertoire parent au path pour importer art
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Tester l'import des nouveaux modèles"""
    print("🧪 TEST DES IMPORTS")
    print("-" * 40)
    
    try:
        from art import app
        from rh_models import (
            # Référentiels
            ReferentielTypeSanction, ReferentielOrigine, ReferentielSituationFamiliale,
            ReferentielGrade, ReferentielCategorieGrade,
            # Données principales
            Personne, Militaire, Ville, Arm, Unite,
            # Relations
            Conjointe, Enfant, Fonction, Medical, Permission
        )
        print("   ✅ Import des modèles réussi")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False

def test_connexion_base():
    """Tester la connexion à la base avec les nouveaux modèles"""
    print("\n🔌 TEST DE CONNEXION À LA BASE")
    print("-" * 40)

    try:
        from art import app
        from db import db  # Utiliser l'instance db de art.py

        with app.app_context():
            # Tester une requête SQL directe d'abord
            result = db.session.execute(db.text("SELECT COUNT(*) FROM referentiel_origine"))
            origines_count = result.scalar()
            print(f"   📊 Origines (SQL direct): {origines_count} enregistrements")

            result = db.session.execute(db.text("SELECT COUNT(*) FROM personne"))
            personnes_count = result.scalar()
            print(f"   📊 Personnes (SQL direct): {personnes_count} enregistrements")

            result = db.session.execute(db.text("SELECT COUNT(*) FROM militaire"))
            militaires_count = result.scalar()
            print(f"   📊 Militaires (SQL direct): {militaires_count} enregistrements")

            print("   ✅ Connexion et requêtes réussies")
            return True

    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
        return False

def test_creation_donnees():
    """Tester la création de données de test"""
    print("\n📝 TEST DE CRÉATION DE DONNÉES")
    print("-" * 40)

    try:
        from art import app
        from db import db  # Utiliser l'instance db de art.py

        with app.app_context():
            # Vérifier que les référentiels sont peuplés avec SQL direct
            result = db.session.execute(db.text("SELECT id_origine FROM referentiel_origine LIMIT 1"))
            origine_id = result.scalar()

            result = db.session.execute(db.text("SELECT id_situation_familiale FROM referentiel_situation_familiale LIMIT 1"))
            situation_fam_id = result.scalar()

            result = db.session.execute(db.text("SELECT id_grade FROM referentiel_grade LIMIT 1"))
            grade_id = result.scalar()

            result = db.session.execute(db.text("SELECT id_ville FROM ville LIMIT 1"))
            ville_id = result.scalar()

            if not all([origine_id, situation_fam_id, grade_id, ville_id]):
                print("   ⚠️ Référentiels non peuplés - impossible de créer des données de test")
                return False
            
            # Créer une personne de test avec SQL direct
            db.session.execute(db.text("""
                INSERT INTO personne (nom, prenom, nom_arabe, prenom_arabe, date_naissance, email, tel, num_cine, gpe_sanguin)
                VALUES ('ALAMI', 'Mohammed', 'العلمي', 'محمد', '1990-05-15', '<EMAIL>', '0661234567', 'AB123456', 'O+')
            """))

            # Récupérer l'ID de la personne créée
            result = db.session.execute(db.text("SELECT LAST_INSERT_ID()"))
            personne_id = result.scalar()

            # Créer un militaire de test
            db.session.execute(db.text("""
                INSERT INTO militaire (matricule, origine_id, date_engagement, situation_fam_id, fonction, id_pers, id_grade, statut)
                VALUES ('TEST001', :origine_id, '2015-01-10', :situation_fam_id, 'Soldat', :personne_id, :grade_id, 'Actif')
            """), {
                'origine_id': origine_id,
                'situation_fam_id': situation_fam_id,
                'personne_id': personne_id,
                'grade_id': grade_id
            })

            db.session.commit()

            print(f"   ✅ Personne créée: Mohammed ALAMI (ID: {personne_id})")
            print(f"   ✅ Militaire créé: TEST001")

            # Tester une requête de jointure
            result = db.session.execute(db.text("""
                SELECT p.nom, p.prenom, m.matricule, g.designation as grade, o.designation as origine
                FROM personne p
                JOIN militaire m ON p.id_pers = m.id_pers
                JOIN referentiel_grade g ON m.id_grade = g.id_grade
                JOIN referentiel_origine o ON m.origine_id = o.id_origine
                WHERE m.matricule = 'TEST001'
            """))

            row = result.fetchone()
            if row:
                print(f"   🔗 Jointure réussie: {row[1]} {row[0]}, {row[2]}, Grade: {row[3]}, Origine: {row[4]}")

            # Nettoyer les données de test
            db.session.execute(db.text("DELETE FROM militaire WHERE matricule = 'TEST001'"))
            db.session.execute(db.text("DELETE FROM personne WHERE id_pers = :personne_id"), {'personne_id': personne_id})
            db.session.commit()

            print("   🧹 Données de test nettoyées")
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur création données: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_relations_complexes():
    """Tester les relations complexes entre tables"""
    print("\n🔗 TEST DES RELATIONS COMPLEXES")
    print("-" * 40)

    try:
        from art import app
        from db import db

        with app.app_context():
            # Tester relation Grade -> Catégorie
            result = db.session.execute(db.text("""
                SELECT g.designation as grade, c.designation as categorie
                FROM referentiel_grade g
                JOIN referentiel_categorie_grade c ON g.id_categorie_grade = c.id_categorie_grade
                LIMIT 1
            """))
            row = result.fetchone()
            if row:
                print(f"   ✅ Grade '{row[0]}' -> Catégorie '{row[1]}'")

            # Tester relation Arm -> Unite
            result = db.session.execute(db.text("""
                SELECT a.designation as arme, COUNT(u.id_organe) as nb_unites
                FROM arm a
                LEFT JOIN unite u ON a.id_arm = u.id_arm
                GROUP BY a.id_arm, a.designation
                LIMIT 1
            """))
            row = result.fetchone()
            if row:
                print(f"   ✅ Arme '{row[0]}' -> {row[1]} unités")

            # Tester relation Unite -> Sous-unités
            result = db.session.execute(db.text("""
                SELECT u.designation as unite, COUNT(s.id_sous_unite) as nb_sous_unites
                FROM unite u
                LEFT JOIN sous_unite s ON u.id_organe = s.id_organe
                GROUP BY u.id_organe, u.designation
                LIMIT 1
            """))
            row = result.fetchone()
            if row:
                print(f"   ✅ Unité '{row[0]}' -> {row[1]} sous-unités")

            return True

    except Exception as e:
        print(f"   ❌ Erreur relations: {e}")
        return False

def test_proprietes_calculees():
    """Tester les propriétés calculées des modèles"""
    print("\n🧮 TEST DES PROPRIÉTÉS CALCULÉES")
    print("-" * 40)
    
    try:
        from art import app
        from rh_models import db, Personne, Militaire
        
        with app.app_context():
            # Créer une personne temporaire pour tester
            personne = Personne(
                nom="TEST",
                prenom="User",
                nom_arabe="تست",
                prenom_arabe="مستخدم",
                date_naissance=date(1985, 3, 20)
            )
            
            # Tester les propriétés sans sauvegarder
            print(f"   ✅ Nom complet: {personne.nom_complet}")
            print(f"   ✅ Nom complet arabe: {personne.nom_complet_arabe}")
            print(f"   ✅ Âge: {personne.age} ans")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur propriétés: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TEST DES NOUVEAUX MODÈLES RH")
    print("Architecture complète (36 tables)")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Connexion base", test_connexion_base),
        ("Création données", test_creation_donnees),
        ("Relations complexes", test_relations_complexes),
        ("Propriétés calculées", test_proprietes_calculees)
    ]
    
    resultats = []
    
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
        except Exception as e:
            print(f"\n❌ ERREUR CRITIQUE dans {nom_test}: {e}")
            resultats.append((nom_test, False))
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS:")
    
    succes = 0
    for nom, resultat in resultats:
        status = "✅" if resultat else "❌"
        print(f"   {status} {nom}")
        if resultat:
            succes += 1
    
    print(f"\n📊 SCORE: {succes}/{len(tests)} tests réussis")
    
    if succes == len(tests):
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("💡 Les nouveaux modèles sont opérationnels")
        print("🚀 Vous pouvez maintenant adapter les routes et templates")
    else:
        print(f"\n⚠️ {len(tests) - succes} tests échoués")
        print("🔧 Vérifiez les erreurs avant de continuer")

if __name__ == "__main__":
    main()

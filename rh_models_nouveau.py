#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nouveaux modèles SQLAlchemy pour l'architecture RH complète (36 tables)
Basé sur architecture_rh_complete.md
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date

# Instance SQLAlchemy (sera initialisée dans art.py)
db = SQLAlchemy()

# =====================================================
# TABLES DE RÉFÉRENCE (13 tables)
# =====================================================

class ReferentielTypeSanction(db.Model):
    __tablename__ = 'referentiel_type_sanction'
    
    id_type_sanction = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    punitions = db.relationship('Punition', backref='type_sanction', lazy=True)

class ReferentielTypeMvt(db.Model):
    __tablename__ = 'referentiel_type_mvt'
    
    id_type_mvt = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    mouvements = db.relationship('Mouvement', backref='type_mvt', lazy=True)

class ReferentielOrigine(db.Model):
    __tablename__ = 'referentiel_origine'
    
    id_origine = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    militaires = db.relationship('Militaire', backref='origine', lazy=True)
    mouvements = db.relationship('Mouvement', backref='origine_mvt', lazy=True)

class ReferentielLieuMedical(db.Model):
    __tablename__ = 'referentiel_lieu_medical'
    
    id_lieu_medical = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    
    # Relations
    medicals = db.relationship('Medical', backref='lieu_medical', lazy=True)

class ReferentielEtatMedical(db.Model):
    __tablename__ = 'referentiel_etat_medical'
    
    id_etat_medical = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(20), nullable=False)
    
    # Relations
    medicals = db.relationship('Medical', backref='etat_medical', lazy=True)

class ReferentielTypeMedical(db.Model):
    __tablename__ = 'referentiel_type_medical'
    
    id_type_medical = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    medicals = db.relationship('Medical', backref='type_medical', lazy=True)

class ReferentielCodeMaladie(db.Model):
    __tablename__ = 'referentiel_code_maladie'
    
    id_code_maladie = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    
    # Relations
    medicals = db.relationship('Medical', backref='code_maladie', lazy=True)

class ReferentielSituationFamiliale(db.Model):
    __tablename__ = 'referentiel_situation_familiale'
    
    id_situation_familiale = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(20), nullable=False)
    
    # Relations
    militaires = db.relationship('Militaire', backref='situation_familiale', lazy=True)

class ReferentielTypePermission(db.Model):
    __tablename__ = 'referentiel_type_permission'
    
    id_type_permission = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    permissions = db.relationship('Permission', backref='type_permission', lazy=True)

class ReferentielTypeAbsence(db.Model):
    __tablename__ = 'referentiel_type_absence'
    
    id_type_absence = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    absences = db.relationship('Absence', backref='type_absence', lazy=True)

class ReferentielTypeFonction(db.Model):
    __tablename__ = 'referentiel_type_fonction'
    
    id_type_fonction = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    bulle_condi = db.Column(db.Text)
    
    # Relations
    fonctions = db.relationship('Fonction', backref='type_fonction', lazy=True)

class ReferentielTypeNote(db.Model):
    __tablename__ = 'referentiel_type_note'
    
    id_type_note = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(50), nullable=False)
    
    # Relations
    notations = db.relationship('Notation', backref='type_note', lazy=True)

class ReferentielCategorieGrade(db.Model):
    __tablename__ = 'referentiel_categorie_grade'
    
    id_categorie_grade = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    
    # Relations
    grades = db.relationship('ReferentielGrade', backref='categorie_grade', lazy=True)

# =====================================================
# TABLES DE DONNÉES (23 tables)
# =====================================================

class Ville(db.Model):
    __tablename__ = 'Ville'
    
    id_ville = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    province = db.Column(db.String(100))
    designation_arabe = db.Column(db.String(100))
    
    # Relations
    adresses = db.relationship('Adresse', backref='ville', lazy=True)

class Personne(db.Model):
    __tablename__ = 'Personne'
    
    id_pers = db.Column(db.Integer, primary_key=True, autoincrement=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    nom_arabe = db.Column(db.String(100), nullable=False)
    prenom_arabe = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    date_deces = db.Column(db.Date)
    email = db.Column(db.String(150))
    tel = db.Column(db.String(50))
    scan_image = db.Column(db.LargeBinary)  # LONGBLOB
    surnom = db.Column(db.String(100))
    num_passport = db.Column(db.String(50))
    num_cine = db.Column(db.String(50))
    scan_cine = db.Column(db.LargeBinary)  # LONGBLOB
    scan_passport = db.Column(db.LargeBinary)  # LONGBLOB
    scan_acte_nais = db.Column(db.LargeBinary)  # LONGBLOB
    gpe_sanguin = db.Column(db.String(10))
    
    # Relations
    adresses = db.relationship('Adresse', backref='personne', lazy=True, cascade='all, delete-orphan')
    militaire = db.relationship('Militaire', backref='personne', uselist=False, cascade='all, delete-orphan')
    conjointe = db.relationship('Conjointe', backref='personne_conjointe', uselist=False, cascade='all, delete-orphan')
    enfant = db.relationship('Enfant', backref='personne_enfant', uselist=False, cascade='all, delete-orphan')
    
    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"
    
    @property
    def nom_complet_arabe(self):
        return f"{self.prenom_arabe} {self.nom_arabe}"
    
    @property
    def age(self):
        if self.date_naissance:
            today = date.today()
            return today.year - self.date_naissance.year - ((today.month, today.day) < (self.date_naissance.month, self.date_naissance.day))
        return None

class Adresse(db.Model):
    __tablename__ = 'Adresse'
    
    id_adresse = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(200), nullable=False)
    designation_arabe = db.Column(db.String(200))
    id_ville = db.Column(db.Integer, db.ForeignKey('Ville.id_ville'), nullable=False)
    id_pers = db.Column(db.Integer, db.ForeignKey('Personne.id_pers'), nullable=False)

class Arm(db.Model):
    __tablename__ = 'Arm'
    
    id_arm = db.Column(db.Integer, primary_key=True, autoincrement=True)
    specialite = db.Column(db.String(100))
    designation = db.Column(db.String(100), nullable=False)
    
    # Relations
    unites = db.relationship('Unite', backref='arm', lazy=True)

class Unite(db.Model):
    __tablename__ = 'Unite'
    
    id_organe = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    id_arm = db.Column(db.Integer, db.ForeignKey('Arm.id_arm'))
    
    # Relations
    sous_unites = db.relationship('SousUnite', backref='unite', lazy=True)
    militaires = db.relationship('Militaire', backref='unite', lazy=True)

class SousUnite(db.Model):
    __tablename__ = 'Sous_unite'
    
    id_sous_unite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    id_organe = db.Column(db.Integer, db.ForeignKey('Unite.id_organe'), nullable=False)

class ReferentielGrade(db.Model):
    __tablename__ = 'referentiel_grade'
    
    id_grade = db.Column(db.Integer, primary_key=True, autoincrement=True)
    designation = db.Column(db.String(100), nullable=False)
    id_categorie_grade = db.Column(db.Integer, db.ForeignKey('referentiel_categorie_grade.id_categorie_grade'), nullable=False)
    
    # Relations
    militaires = db.relationship('Militaire', backref='grade', lazy=True)
    promotions_precedent = db.relationship('Promotion', foreign_keys='Promotion.id_grade_precedent', backref='grade_precedent', lazy=True)
    promotions_suivant = db.relationship('Promotion', foreign_keys='Promotion.id_grade_suivant', backref='grade_suivant', lazy=True)

class Militaire(db.Model):
    __tablename__ = 'Militaire'
    
    id_e_mili = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), nullable=False, unique=True)
    num_mutuel = db.Column(db.String(50))
    origine_id = db.Column(db.Integer, db.ForeignKey('referentiel_origine.id_origine'), nullable=False)
    statut = db.Column(db.String(50))
    date_engagement = db.Column(db.Date, nullable=False)
    situation_fam_id = db.Column(db.Integer, db.ForeignKey('referentiel_situation_familiale.id_situation_familiale'), nullable=False)
    fonction = db.Column(db.String(100))
    id_pers = db.Column(db.Integer, db.ForeignKey('Personne.id_pers'), nullable=False, unique=True)
    id_grade = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    id_organe = db.Column(db.Integer, db.ForeignKey('Unite.id_organe'))
    
    # Relations
    conjointes = db.relationship('Conjointe', backref='militaire', lazy=True, cascade='all, delete-orphan')
    enfants = db.relationship('Enfant', backref='militaire', lazy=True, cascade='all, delete-orphan')
    fonctions = db.relationship('Fonction', backref='militaire', lazy=True, cascade='all, delete-orphan')
    notations = db.relationship('Notation', backref='militaire', lazy=True, cascade='all, delete-orphan')
    mouvements = db.relationship('Mouvement', backref='militaire', lazy=True, cascade='all, delete-orphan')
    punitions = db.relationship('Punition', backref='militaire', lazy=True, cascade='all, delete-orphan')
    medicals = db.relationship('Medical', backref='militaire', lazy=True, cascade='all, delete-orphan')
    permissions = db.relationship('Permission', backref='militaire', lazy=True, cascade='all, delete-orphan')
    absences = db.relationship('Absence', backref='militaire', lazy=True, cascade='all, delete-orphan')
    promotions = db.relationship('Promotion', backref='militaire', lazy=True, cascade='all, delete-orphan')
    evenements = db.relationship('Evenement', backref='militaire', lazy=True, cascade='all, delete-orphan')
    permanences = db.relationship('Permanence', backref='militaire', lazy=True, cascade='all, delete-orphan')
    taches = db.relationship('Tache', backref='militaire', lazy=True, cascade='all, delete-orphan')
    
    @property
    def nom_complet(self):
        return self.personne.nom_complet if self.personne else ""
    
    @property
    def anciennete_service(self):
        if self.date_engagement:
            today = date.today()
            return today.year - self.date_engagement.year
        return 0

class Conjointe(db.Model):
    __tablename__ = 'Conjointe'

    id_conjointe = db.Column(db.Integer, db.ForeignKey('Personne.id_pers'), primary_key=True)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)
    scan_acte_mariage = db.Column(db.LargeBinary)  # LONGBLOB

class Enfant(db.Model):
    __tablename__ = 'Enfant'

    id_enfant = db.Column(db.Integer, db.ForeignKey('Personne.id_pers'), primary_key=True)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)
    sexe = db.Column(db.String(10), nullable=False)

class Fonction(db.Model):
    __tablename__ = 'Fonction'

    id_fonction = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date)
    reference = db.Column(db.String(100))
    decision = db.Column(db.String(100))
    date_decision = db.Column(db.Date)
    id_type_fonction = db.Column(db.Integer, db.ForeignKey('referentiel_type_fonction.id_type_fonction'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Notation(db.Model):
    __tablename__ = 'Notation'

    id_note = db.Column(db.Integer, primary_key=True, autoincrement=True)
    note = db.Column(db.String(200))
    date = db.Column(db.Date)
    tenue = db.Column(db.String(100))
    disponibilite = db.Column(db.String(50))
    moralite = db.Column(db.String(50))
    autorite = db.Column(db.String(50))
    execution = db.Column(db.String(50))
    qualification = db.Column(db.String(50))
    adaptation = db.Column(db.String(50))
    rendement = db.Column(db.String(50))
    sit_medical = db.Column(db.String(50))
    esprit_equipe = db.Column(db.String(50))
    discipline = db.Column(db.String(50))
    condi_physique = db.Column(db.String(50))
    commandement = db.Column(db.String(50))
    scan_note = db.Column(db.LargeBinary)  # LONGBLOB
    observation = db.Column(db.Text)
    id_type_note = db.Column(db.Integer, db.ForeignKey('referentiel_type_note.id_type_note'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Mission(db.Model):
    __tablename__ = 'Mission'

    id_mission = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date_debut = db.Column(db.Date)
    date_fin = db.Column(db.Date)
    designation = db.Column(db.String(100))
    reference = db.Column(db.String(100))

class Mouvement(db.Model):
    __tablename__ = 'Mouvement'

    id_mvt = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date = db.Column(db.Date, nullable=False)
    scan_demande = db.Column(db.LargeBinary)  # LONGBLOB
    reference = db.Column(db.String(100))
    id_type_mvt = db.Column(db.Integer, db.ForeignKey('referentiel_type_mvt.id_type_mvt'), nullable=False)
    origine_id = db.Column(db.Integer, db.ForeignKey('referentiel_origine.id_origine'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Punition(db.Model):
    __tablename__ = 'Punition'

    id_sanction = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date = db.Column(db.Date, nullable=False)
    motif = db.Column(db.String(200))
    observation = db.Column(db.Text)
    duree = db.Column(db.Integer)
    reference = db.Column(db.String(100))
    id_type_sanction = db.Column(db.Integer, db.ForeignKey('referentiel_type_sanction.id_type_sanction'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

    # Relations
    jugements = db.relationship('Jugement', backref='punition', lazy=True, cascade='all, delete-orphan')

class Jugement(db.Model):
    __tablename__ = 'Jugement'

    id_jugement = db.Column(db.Integer, primary_key=True, autoincrement=True)
    nom_tribunal = db.Column(db.String(100))
    num_dossier = db.Column(db.String(50))
    type_jugement = db.Column(db.String(50))
    decision = db.Column(db.String(200))
    reference = db.Column(db.String(100))
    date_jugement = db.Column(db.Date)
    id_sanction = db.Column(db.Integer, db.ForeignKey('Punition.id_sanction'))

class Medical(db.Model):
    __tablename__ = 'Medical'

    id_medical = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date)
    duree = db.Column(db.Integer)
    reference = db.Column(db.String(100))
    observation = db.Column(db.Text)
    etat_id = db.Column(db.Integer, db.ForeignKey('referentiel_etat_medical.id_etat_medical'), nullable=False)
    id_lieu_medical = db.Column(db.Integer, db.ForeignKey('referentiel_lieu_medical.id_lieu_medical'), nullable=False)
    id_type_medical = db.Column(db.Integer, db.ForeignKey('referentiel_type_medical.id_type_medical'), nullable=False)
    id_code_maladie = db.Column(db.Integer, db.ForeignKey('referentiel_code_maladie.id_code_maladie'))
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Permission(db.Model):
    __tablename__ = 'Permission'

    id_permission = db.Column(db.Integer, primary_key=True, autoincrement=True)
    num_serie = db.Column(db.BigInteger, nullable=False)
    date_demande = db.Column(db.DateTime, nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    motif = db.Column(db.String(200))
    approbation_chef = db.Column(db.Boolean, nullable=False)
    adresse = db.Column(db.String(200))
    scan_demande = db.Column(db.LargeBinary)  # LONGBLOB
    duree = db.Column(db.Integer)
    id_type_permission = db.Column(db.Integer, db.ForeignKey('referentiel_type_permission.id_type_permission'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Absence(db.Model):
    __tablename__ = 'Absence'

    id_absence = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date_absence = db.Column(db.Date, nullable=False)
    date_rejoint = db.Column(db.Date)
    motif = db.Column(db.String(200))
    reference = db.Column(db.String(100))
    lieu = db.Column(db.String(200))
    id_type_absence = db.Column(db.Integer, db.ForeignKey('referentiel_type_absence.id_type_absence'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Promotion(db.Model):
    __tablename__ = 'Promotion'

    id_promotion = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date = db.Column(db.Date, nullable=False)
    reference = db.Column(db.String(100))
    type = db.Column(db.String(50))
    observation = db.Column(db.Text)
    id_grade_precedent = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    id_grade_suivant = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Evenement(db.Model):
    __tablename__ = 'Evenement'

    id_evenement = db.Column(db.Integer, primary_key=True, autoincrement=True)
    objet = db.Column(db.String(200))
    reference = db.Column(db.String(100))
    date = db.Column(db.Date)
    scan_doc = db.Column(db.LargeBinary)  # LONGBLOB
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Permanence(db.Model):
    __tablename__ = 'Permanence'

    id_permanence = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date = db.Column(db.Date, nullable=False)
    tour_aid = db.Column(db.String(50))
    tour = db.Column(db.String(50))
    observation = db.Column(db.Text)
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

class Tache(db.Model):
    __tablename__ = 'Tache'

    id_tache = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(200))
    observation = db.Column(db.Text)
    etat = db.Column(db.String(50))
    id_e_mili = db.Column(db.Integer, db.ForeignKey('Militaire.id_e_mili'), nullable=False)

# =====================================================
# FONCTIONS UTILITAIRES
# =====================================================

def init_db_rh(app):
    """Initialiser la base de données RH avec l'application Flask"""
    db.init_app(app)

def get_all_models():
    """Retourner tous les modèles RH"""
    return [
        # Référentiels
        ReferentielTypeSanction, ReferentielTypeMvt, ReferentielOrigine,
        ReferentielLieuMedical, ReferentielEtatMedical, ReferentielTypeMedical,
        ReferentielCodeMaladie, ReferentielSituationFamiliale, ReferentielTypePermission,
        ReferentielTypeAbsence, ReferentielTypeFonction, ReferentielTypeNote,
        ReferentielCategorieGrade,
        # Données
        Ville, Personne, Adresse, Arm, Unite, SousUnite, ReferentielGrade,
        Militaire, Conjointe, Enfant, Fonction, Notation, Mission, Mouvement,
        Punition, Jugement, Medical, Permission, Absence, Promotion, Evenement,
        Permanence, Tache
    ]

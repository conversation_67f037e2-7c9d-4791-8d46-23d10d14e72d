{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Enfants - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-child"></i>
                                Gestion des Enfants
                            </h2>
                            <small style="color: var(--text-light);">
                                {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }} {{ militaire.nom_complet }} - {{ militaire.matricule }}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <form method="POST" action="{{ url_for('rh.modifier_enfants', matricule=militaire.matricule) }}">

        <!-- Section 1: Ajouter un nouvel enfant -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-plus"></i>
                            Ajouter un Nouvel Enfant
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Nom complet -->
                            <div class="col-md-4 mb-3">
                                <label for="nouveau_nom_complet" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom Complet
                                </label>
                                <input type="text" class="form-control" id="nouveau_nom_complet"
                                       name="nouveau_nom_complet" placeholder="Prénom Nom de l'enfant">
                            </div>

                            <!-- Sexe -->
                            <div class="col-md-2 mb-3">
                                <label for="nouveau_sexe_id" class="form-label">
                                    <i class="fas fa-venus-mars me-1"></i>Sexe
                                </label>
                                <select class="form-select" id="nouveau_sexe_id" name="nouveau_sexe_id">
                                    <option value="">-- Sélectionner --</option>
                                    {% for genre in genres %}
                                    <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Date naissance -->
                            <div class="col-md-2 mb-3">
                                <label for="nouveau_date_naissance" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date Naissance
                                </label>
                                <input type="date" class="form-control" id="nouveau_date_naissance"
                                       name="nouveau_date_naissance">
                            </div>

                            <!-- Lieu naissance -->
                            <div class="col-md-2 mb-3">
                                <label for="nouveau_lieu_naissance" class="form-label">
                                    <i class="fas fa-map-pin me-1"></i>Lieu Naissance
                                </label>
                                <input type="text" class="form-control" id="nouveau_lieu_naissance"
                                       name="nouveau_lieu_naissance" placeholder="Ville">
                            </div>

                            <!-- Date décès -->
                            <div class="col-md-2 mb-3">
                                <label for="nouveau_date_deces" class="form-label">
                                    Date Décès <small class="text-muted">(optionnel)</small>
                                </label>
                                <input type="date" class="form-control" id="nouveau_date_deces"
                                       name="nouveau_date_deces">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Section 2: Modifier les enfants existants -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i>
                            Liste des Enfants ({{ enfants|length }})
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if enfants %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Nom Complet</th>
                                        <th>Sexe</th>
                                        <th>Date Naissance</th>
                                        <th>Âge</th>
                                        <th>Lieu Naissance</th>
                                        <th>Modifier Date Décès</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for enfant in enfants %}
                                    <tr>
                                        <td><strong>{{ enfant.nom }} {{ enfant.prenom }}</strong></td>
                                        <td>
                                            {% if enfant.genre %}
                                            <span class="badge bg-{{ 'primary' if enfant.genre.libelle == 'Masculin' else 'danger' }}">
                                                <i class="fas fa-{{ 'mars' if enfant.genre.libelle == 'Masculin' else 'venus' }}"></i>
                                                {{ enfant.genre.libelle }}
                                            </span>
                                            {% else %}
                                            <span class="badge bg-secondary">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ enfant.date_naissance.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            {% set age = ((date.today() - enfant.date_naissance).days // 365) %}
                                            <strong>{{ age }}</strong> ans
                                        </td>
                                        <td>{{ enfant.lieu_naissance }}</td>
                                        <td>
                                            <input type="date" class="form-control form-control-sm"
                                                   name="enfant_{{ enfant.id_enfant }}_date_deces"
                                                   value="{{ enfant.date_deces.strftime('%Y-%m-%d') if enfant.date_deces else '' }}"
                                                   data-naissance="{{ enfant.date_naissance.strftime('%Y-%m-%d') }}">
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-child fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Aucun enfant enregistré</h6>
                            <p class="text-muted">Utilisez la section ci-dessus pour ajouter le premier enfant</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <!-- Boutons d'action -->
        <div class="text-center mt-4">
            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}"
               class="btn btn-secondary me-3">
                <i class="fas fa-times me-2"></i>Annuler
            </a>
            <button type="submit" class="btn btn-success-military">
                <i class="fas fa-save me-2"></i>Enregistrer les modifications
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');

    if (form) {
        form.addEventListener('submit', function(e) {
            // Validation du nouvel enfant
            const nouveauNom = document.getElementById('nouveau_nom_complet').value.trim();
            const nouveauSexe = document.getElementById('nouveau_sexe_id').value;
            const nouveauDateNaissance = document.getElementById('nouveau_date_naissance').value;
            const nouveauDateDeces = document.getElementById('nouveau_date_deces').value;

            if (nouveauNom || nouveauSexe || nouveauDateNaissance) {
                if (!nouveauNom) {
                    e.preventDefault();
                    alert('Le nom complet du nouvel enfant est obligatoire.');
                    return false;
                }
                if (!nouveauSexe) {
                    e.preventDefault();
                    alert('Le sexe du nouvel enfant est obligatoire.');
                    return false;
                }
                if (!nouveauDateNaissance) {
                    e.preventDefault();
                    alert('La date de naissance du nouvel enfant est obligatoire.');
                    return false;
                }

                // Validation date décès vs date naissance pour nouvel enfant
                if (nouveauDateDeces && nouveauDateNaissance) {
                    if (new Date(nouveauDateDeces) <= new Date(nouveauDateNaissance)) {
                        e.preventDefault();
                        alert('La date de décès doit être postérieure à la date de naissance.');
                        return false;
                    }
                }
            }

            // Validation des dates de décès pour enfants existants
            const dateInputs = document.querySelectorAll('input[name*="_date_deces"]');
            for (let input of dateInputs) {
                if (input.value) {
                    const dateNaissance = input.getAttribute('data-naissance');
                    if (new Date(input.value) <= new Date(dateNaissance)) {
                        e.preventDefault();
                        alert('La date de décès doit être postérieure à la date de naissance.');
                        input.focus();
                        return false;
                    }
                }
            }
        });
    }

    // Animation des statistiques
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 20;

        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });
});
</script>
{% endblock %}

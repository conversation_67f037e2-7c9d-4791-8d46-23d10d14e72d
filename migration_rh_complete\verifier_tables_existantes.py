#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier quelles tables de l'architecture complète existent déjà
"""

import mysql.connector
from mysql.connector import Error

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

# Tables attendues selon l'architecture complète
TABLES_ARCHITECTURE_COMPLETE = [
    # Référentiels (13)
    'referentiel_type_sanction', 'referentiel_type_mvt', 'referentiel_origine',
    'referentiel_lieu_medical', 'referentiel_etat_medical', 'referentiel_type_medical',
    'referentiel_code_maladie', 'referentiel_situation_familiale', 'referentiel_type_permission',
    'referentiel_type_absence', 'referentiel_type_fonction', 'referentiel_type_note',
    'referentiel_categorie_grade',
    # Données (23)
    'Ville', 'Personne', 'Adresse', 'Arm', 'Unite', 'Sous_unite', 'referentiel_grade',
    'Militaire', 'Conjointe', 'Enfant', 'Fonction', 'Notation', 'Mission', 'Mouvement',
    'Punition', 'Jugement', 'Medical', 'Permission', 'Absence', 'Promotion', 'Evenement',
    'Permanence', 'Tache'
]

def verifier_tables():
    """Vérifier quelles tables existent dans la base"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Lister toutes les tables
        cursor.execute("SHOW TABLES")
        tables_existantes = [table[0].lower() for table in cursor.fetchall()]
        
        print("🔍 VÉRIFICATION DES TABLES DE L'ARCHITECTURE COMPLÈTE")
        print("=" * 70)
        
        tables_presentes = []
        tables_manquantes = []
        
        for table in TABLES_ARCHITECTURE_COMPLETE:
            if table.lower() in tables_existantes:
                tables_presentes.append(table)
                print(f"   ✅ {table}")
            else:
                tables_manquantes.append(table)
                print(f"   ❌ {table}")
        
        print("\n" + "=" * 70)
        print(f"📊 RÉSUMÉ:")
        print(f"   Tables présentes: {len(tables_presentes)}/36")
        print(f"   Tables manquantes: {len(tables_manquantes)}/36")
        
        if tables_manquantes:
            print(f"\n⚠️ TABLES MANQUANTES ({len(tables_manquantes)}):")
            for table in tables_manquantes:
                print(f"      - {table}")
        
        if len(tables_presentes) == 36:
            print("\n🎉 TOUTES LES TABLES SONT PRÉSENTES!")
        
        # Vérifier la structure de quelques tables clés
        print(f"\n🔍 VÉRIFICATION DE LA STRUCTURE DES TABLES CLÉS:")
        
        tables_cles = ['Personne', 'Militaire', 'Conjointe', 'Enfant']
        for table in tables_cles:
            if table.lower() in tables_existantes:
                cursor.execute(f"DESCRIBE {table}")
                colonnes = cursor.fetchall()
                print(f"\n   📋 Table {table} ({len(colonnes)} colonnes):")
                for colonne in colonnes[:5]:  # Afficher les 5 premières colonnes
                    print(f"      - {colonne[0]} ({colonne[1]})")
                if len(colonnes) > 5:
                    print(f"      ... et {len(colonnes) - 5} autres colonnes")
        
        return len(tables_presentes), len(tables_manquantes)
        
    except Error as e:
        print(f"❌ Erreur: {e}")
        return 0, 0
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def verifier_donnees():
    """Vérifier s'il y a des données dans les tables"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print(f"\n📊 VÉRIFICATION DES DONNÉES:")
        
        tables_avec_donnees = [
            'referentiel_type_sanction', 'referentiel_origine', 'referentiel_situation_familiale',
            'Personne', 'Militaire', 'Arm', 'Unite', 'referentiel_grade'
        ]
        
        for table in tables_avec_donnees:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"   📈 {table}: {count} enregistrements")
                else:
                    print(f"   📭 {table}: vide")
            except Error:
                print(f"   ❌ {table}: table inexistante")
        
    except Error as e:
        print(f"❌ Erreur vérification données: {e}")
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 VÉRIFICATION DE L'ARCHITECTURE RH COMPLÈTE")
    print("Analyse de l'état actuel de la base de données")
    print("=" * 70)
    
    presentes, manquantes = verifier_tables()
    verifier_donnees()
    
    print("\n" + "=" * 70)
    if manquantes == 0:
        print("🎉 ARCHITECTURE COMPLÈTE PRÊTE!")
        print("💡 Vous pouvez passer à l'étape suivante: population des référentiels")
    else:
        print(f"⚠️ {manquantes} tables manquantes")
        print("💡 Certaines tables existent déjà, d'autres doivent être créées")

if __name__ == "__main__":
    main()

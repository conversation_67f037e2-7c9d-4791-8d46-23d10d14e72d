#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes de gestion des absences pour le module RH
Implémentation des routes pour PTC, permissions, désertions, détachements
"""

from flask import request, redirect, url_for, flash, jsonify
from datetime import datetime, timedelta
from rh_models import *
from rh_blueprint import rh_bp

# ============================================================================
# ROUTES DE GESTION DES ABSENCES
# ============================================================================

@rh_bp.route('/api/ajouter_ptc', methods=['POST'])
def api_ajouter_ptc():
    """API pour ajouter un PTC"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer le PTC
        ptc = Ptc(
            matricule=data['matricule'],
            date_ptc=datetime.strptime(data['date_ptc'], '%Y-%m-%d').date(),
            duree=data['duree'],
            date_debut=datetime.strptime(data['date_debut'], '%Y-%m-%d').date(),
            date_fin=datetime.strptime(data['date_fin'], '%Y-%m-%d').date(),
            objet=data['objet'],
            observations=data.get('observations')
        )
        
        db.session.add(ptc)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'PTC ajouté avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_permission', methods=['POST'])
def api_ajouter_permission():
    """API pour ajouter une permission"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la permission
        permission = Permission(
            matricule=data['matricule'],
            date_debut=datetime.strptime(data['date_debut'], '%Y-%m-%d').date(),
            date_fin=datetime.strptime(data['date_fin'], '%Y-%m-%d').date(),
            adresse=data['adresse'],
            numero_serie=data['numero_serie']
        )
        
        db.session.add(permission)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Permission ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_desertion', methods=['POST'])
def api_ajouter_desertion():
    """API pour ajouter une désertion"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la désertion
        desertion = Desertion(
            matricule=data['matricule'],
            date_absence=datetime.strptime(data['date_absence'], '%Y-%m-%d').date(),
            date_desertion=datetime.strptime(data['date_desertion'], '%Y-%m-%d').date(),
            date_retour=datetime.strptime(data['date_retour'], '%Y-%m-%d').date(),
            date_arret_solde=datetime.strptime(data['date_arret_solde'], '%Y-%m-%d').date() if data.get('date_arret_solde') else None,
            date_prise_solde=datetime.strptime(data['date_prise_solde'], '%Y-%m-%d').date() if data.get('date_prise_solde') else None
        )
        
        db.session.add(desertion)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Désertion ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_detachement', methods=['POST'])
def api_ajouter_detachement():
    """API pour ajouter un détachement"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer le détachement
        detachement = Detachement(
            matricule=data['matricule'],
            date_debut=datetime.strptime(data['date_debut'], '%Y-%m-%d').date(),
            adresse_detachement=data['adresse_detachement'],
            pays=data['pays'],
            date_fin=datetime.strptime(data['date_fin'], '%Y-%m-%d').date()
        )
        
        db.session.add(detachement)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Détachement ajouté avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/modifier_ptc/<int:id_ptc>', methods=['PUT'])
def api_modifier_ptc(id_ptc):
    """API pour modifier un PTC"""
    try:
        ptc = Ptc.query.get_or_404(id_ptc)
        data = request.get_json()
        
        # Mise à jour des champs
        if 'date_ptc' in data:
            ptc.date_ptc = datetime.strptime(data['date_ptc'], '%Y-%m-%d').date()
        if 'duree' in data:
            ptc.duree = data['duree']
        if 'date_debut' in data:
            ptc.date_debut = datetime.strptime(data['date_debut'], '%Y-%m-%d').date()
        if 'date_fin' in data:
            ptc.date_fin = datetime.strptime(data['date_fin'], '%Y-%m-%d').date()
        if 'objet' in data:
            ptc.objet = data['objet']
        if 'observations' in data:
            ptc.observations = data['observations']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'PTC modifié avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_ptc/<int:id_ptc>', methods=['DELETE'])
def api_supprimer_ptc(id_ptc):
    """API pour supprimer un PTC"""
    try:
        ptc = Ptc.query.get_or_404(id_ptc)
        
        db.session.delete(ptc)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'PTC supprimé avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/absences', methods=['GET'])
def api_get_absences_personnel(matricule):
    """API pour récupérer toutes les absences d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # PTC
        ptcs_data = []
        for ptc in personnel.ptcs:
            ptcs_data.append({
                'id_ptc': ptc.id_ptc,
                'date_ptc': ptc.date_ptc.strftime('%Y-%m-%d'),
                'duree': ptc.duree,
                'date_debut': ptc.date_debut.strftime('%Y-%m-%d'),
                'date_fin': ptc.date_fin.strftime('%Y-%m-%d'),
                'objet': ptc.objet,
                'observations': ptc.observations,
                'en_cours': ptc.date_debut <= datetime.now().date() <= ptc.date_fin
            })
        
        # Permissions
        permissions_data = []
        for permission in personnel.permissions:
            permissions_data.append({
                'id_permission': permission.id_permission,
                'date_debut': permission.date_debut.strftime('%Y-%m-%d'),
                'date_fin': permission.date_fin.strftime('%Y-%m-%d'),
                'adresse': permission.adresse,
                'numero_serie': permission.numero_serie,
                'duree_jours': (permission.date_fin - permission.date_debut).days + 1,
                'en_cours': permission.date_debut <= datetime.now().date() <= permission.date_fin
            })
        
        # Désertions
        desertions_data = []
        for desertion in personnel.desertions:
            desertions_data.append({
                'id_desertion': desertion.id_desertion,
                'date_absence': desertion.date_absence.strftime('%Y-%m-%d'),
                'date_desertion': desertion.date_desertion.strftime('%Y-%m-%d'),
                'date_retour': desertion.date_retour.strftime('%Y-%m-%d'),
                'date_arret_solde': desertion.date_arret_solde.strftime('%Y-%m-%d') if desertion.date_arret_solde else None,
                'date_prise_solde': desertion.date_prise_solde.strftime('%Y-%m-%d') if desertion.date_prise_solde else None,
                'duree_jours': (desertion.date_retour - desertion.date_desertion).days
            })
        
        # Détachements
        detachements_data = []
        for detachement in personnel.detachements:
            detachements_data.append({
                'id_detachement': detachement.id_detachement,
                'date_debut': detachement.date_debut.strftime('%Y-%m-%d'),
                'date_fin': detachement.date_fin.strftime('%Y-%m-%d'),
                'adresse_detachement': detachement.adresse_detachement,
                'pays': detachement.pays,
                'duree_jours': (detachement.date_fin - detachement.date_debut).days + 1,
                'en_cours': detachement.date_debut <= datetime.now().date() <= detachement.date_fin
            })
        
        # Trier par date (plus récent en premier)
        ptcs_data.sort(key=lambda x: x['date_debut'], reverse=True)
        permissions_data.sort(key=lambda x: x['date_debut'], reverse=True)
        desertions_data.sort(key=lambda x: x['date_desertion'], reverse=True)
        detachements_data.sort(key=lambda x: x['date_debut'], reverse=True)
        
        # Statistiques
        ptc_en_cours = len([p for p in ptcs_data if p['en_cours']])
        permissions_en_cours = len([p for p in permissions_data if p['en_cours']])
        detachements_en_cours = len([d for d in detachements_data if d['en_cours']])
        
        return jsonify({
            'success': True,
            'absences': {
                'ptcs': ptcs_data,
                'permissions': permissions_data,
                'desertions': desertions_data,
                'detachements': detachements_data,
                'statistiques': {
                    'nb_ptcs': len(ptcs_data),
                    'nb_permissions': len(permissions_data),
                    'nb_desertions': len(desertions_data),
                    'nb_detachements': len(detachements_data),
                    'ptc_en_cours': ptc_en_cours,
                    'permissions_en_cours': permissions_en_cours,
                    'detachements_en_cours': detachements_en_cours
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/absences_en_cours', methods=['GET'])
def api_absences_en_cours():
    """API pour récupérer toutes les absences en cours"""
    try:
        today = datetime.now().date()
        
        # PTC en cours
        ptcs_en_cours = Ptc.query.filter(
            Ptc.date_debut <= today,
            Ptc.date_fin >= today
        ).all()
        
        # Permissions en cours
        permissions_en_cours = Permission.query.filter(
            Permission.date_debut <= today,
            Permission.date_fin >= today
        ).all()
        
        # Détachements en cours
        detachements_en_cours = Detachement.query.filter(
            Detachement.date_debut <= today,
            Detachement.date_fin >= today
        ).all()
        
        # Formatage des données
        ptcs_data = []
        for ptc in ptcs_en_cours:
            ptcs_data.append({
                'id_ptc': ptc.id_ptc,
                'personnel': {
                    'matricule': ptc.matricule,
                    'nom_complet': ptc.personnel.nom_complet,
                    'unite': ptc.personnel.unite.libelle,
                    'grade': ptc.personnel.grade_actuel.libelle
                },
                'date_debut': ptc.date_debut.strftime('%d/%m/%Y'),
                'date_fin': ptc.date_fin.strftime('%d/%m/%Y'),
                'objet': ptc.objet,
                'jours_restants': (ptc.date_fin - today).days
            })
        
        permissions_data = []
        for permission in permissions_en_cours:
            permissions_data.append({
                'id_permission': permission.id_permission,
                'personnel': {
                    'matricule': permission.matricule,
                    'nom_complet': permission.personnel.nom_complet,
                    'unite': permission.personnel.unite.libelle,
                    'grade': permission.personnel.grade_actuel.libelle
                },
                'date_debut': permission.date_debut.strftime('%d/%m/%Y'),
                'date_fin': permission.date_fin.strftime('%d/%m/%Y'),
                'adresse': permission.adresse,
                'numero_serie': permission.numero_serie,
                'jours_restants': (permission.date_fin - today).days
            })
        
        detachements_data = []
        for detachement in detachements_en_cours:
            detachements_data.append({
                'id_detachement': detachement.id_detachement,
                'personnel': {
                    'matricule': detachement.matricule,
                    'nom_complet': detachement.personnel.nom_complet,
                    'unite': detachement.personnel.unite.libelle,
                    'grade': detachement.personnel.grade_actuel.libelle
                },
                'date_debut': detachement.date_debut.strftime('%d/%m/%Y'),
                'date_fin': detachement.date_fin.strftime('%d/%m/%Y'),
                'pays': detachement.pays,
                'adresse_detachement': detachement.adresse_detachement,
                'jours_restants': (detachement.date_fin - today).days
            })
        
        return jsonify({
            'success': True,
            'absences_en_cours': {
                'ptcs': ptcs_data,
                'permissions': permissions_data,
                'detachements': detachements_data,
                'totaux': {
                    'ptcs': len(ptcs_data),
                    'permissions': len(permissions_data),
                    'detachements': len(detachements_data),
                    'total': len(ptcs_data) + len(permissions_data) + len(detachements_data)
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/statistiques_absences', methods=['GET'])
def api_statistiques_absences():
    """API pour récupérer les statistiques des absences"""
    try:
        # Statistiques générales
        total_ptcs = Ptc.query.count()
        total_permissions = Permission.query.count()
        total_desertions = Desertion.query.count()
        total_detachements = Detachement.query.count()
        
        # Absences en cours
        today = datetime.now().date()
        ptcs_en_cours = Ptc.query.filter(
            Ptc.date_debut <= today,
            Ptc.date_fin >= today
        ).count()
        
        permissions_en_cours = Permission.query.filter(
            Permission.date_debut <= today,
            Permission.date_fin >= today
        ).count()
        
        detachements_en_cours = Detachement.query.filter(
            Detachement.date_debut <= today,
            Detachement.date_fin >= today
        ).count()
        
        # Statistiques par unité pour les absences en cours
        stats_unite_ptc = db.session.query(
            ReferentielUnite.libelle,
            db.func.count(Ptc.id_ptc).label('effectif')
        ).join(Personnel).join(Ptc).filter(
            Ptc.date_debut <= today,
            Ptc.date_fin >= today
        ).group_by(ReferentielUnite.libelle).all()
        
        # Durée moyenne des absences
        duree_moyenne_ptc = db.session.query(
            db.func.avg(Ptc.duree)
        ).scalar() or 0
        
        duree_moyenne_permission = db.session.query(
            db.func.avg(
                db.func.datediff(Permission.date_fin, Permission.date_debut) + 1
            )
        ).scalar() or 0
        
        # Personnel avec le plus d'absences
        personnel_max_absences = db.session.query(
            Personnel.matricule,
            Personnel.nom,
            Personnel.prenom,
            (db.func.count(Ptc.id_ptc) + 
             db.func.count(Permission.id_permission) + 
             db.func.count(Detachement.id_detachement)).label('total_absences')
        ).outerjoin(Ptc).outerjoin(Permission).outerjoin(Detachement).group_by(
            Personnel.matricule, Personnel.nom, Personnel.prenom
        ).order_by(db.text('total_absences DESC')).limit(10).all()
        
        return jsonify({
            'success': True,
            'statistiques': {
                'totaux': {
                    'ptcs': total_ptcs,
                    'permissions': total_permissions,
                    'desertions': total_desertions,
                    'detachements': total_detachements,
                    'total_absences': total_ptcs + total_permissions + total_detachements
                },
                'en_cours': {
                    'ptcs': ptcs_en_cours,
                    'permissions': permissions_en_cours,
                    'detachements': detachements_en_cours,
                    'total': ptcs_en_cours + permissions_en_cours + detachements_en_cours
                },
                'durees_moyennes': {
                    'ptc_jours': round(duree_moyenne_ptc, 1),
                    'permission_jours': round(duree_moyenne_permission, 1)
                },
                'par_unite': [{'unite': s[0], 'effectif': s[1]} for s in stats_unite_ptc],
                'personnel_max_absences': [
                    {
                        'matricule': p[0],
                        'nom_complet': f"{p[2]} {p[1]}",
                        'total_absences': p[3]
                    } for p in personnel_max_absences if p[3] > 0
                ]
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

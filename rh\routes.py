from flask import render_template, request, redirect, url_for, flash, jsonify, session
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_, func, desc
from . import rh_bp
from db import db
from rh_models import *

@rh_bp.route('/')
def dashboard():
    """Dashboard principal de l'application RH"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        total_officiers = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier'
        ).count()
        total_officiers_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier du rang'
        ).count()
        total_militaires_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Militaire du rang'
        ).count()
        
        # Répartition par arme
        repartition_arme = db.session.query(
            ReferentielArme.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielArme.libelle).all()
        
        # Répartition par unité (top 10)
        repartition_unite = db.session.query(
            ReferentielUnite.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielUnite.libelle).order_by(
            desc(func.count(Personnel.matricule))
        ).limit(10).all()
        
        # Personnel en situation médicale inapte
        personnel_inapte = SituationMedicale.query.filter_by(aptitude='inapte').count()
        
        # Sanctions récentes (30 derniers jours)
        date_limite = datetime.now().date() - timedelta(days=30)
        sanctions_recentes = Sanction.query.filter(
            Sanction.date_sanction >= date_limite
        ).count()
        
        # Permissions en cours
        aujourd_hui = datetime.now().date()
        permissions_en_cours = Permission.query.filter(
            and_(Permission.date_debut <= aujourd_hui, Permission.date_fin >= aujourd_hui)
        ).count()
        
        # Détachements en cours
        detachements_en_cours = Detachement.query.filter(
            and_(Detachement.date_debut <= aujourd_hui, Detachement.date_fin >= aujourd_hui)
        ).count()
        
        stats = {
            'total_personnel': total_personnel,
            'total_officiers': total_officiers,
            'total_officiers_rang': total_officiers_rang,
            'total_militaires_rang': total_militaires_rang,
            'repartition_arme': repartition_arme,
            'repartition_unite': repartition_unite,
            'personnel_inapte': personnel_inapte,
            'sanctions_recentes': sanctions_recentes,
            'permissions_en_cours': permissions_en_cours,
            'detachements_en_cours': detachements_en_cours
        }
        
        # Adapter les données pour le nouveau template
        stats_categories = db.session.query(ReferentielCategorie.libelle, func.count(Personnel.matricule))\
                          .join(Personnel).group_by(ReferentielCategorie.libelle).all()

        stats_grades = db.session.query(ReferentielGrade.libelle, func.count(Personnel.matricule))\
                      .join(Personnel).group_by(ReferentielGrade.libelle)\
                      .order_by(func.count(Personnel.matricule).desc()).limit(5).all()

        stats_unites = db.session.query(ReferentielUnite.libelle, func.count(Personnel.matricule))\
                       .join(Personnel).group_by(ReferentielUnite.libelle)\
                       .order_by(func.count(Personnel.matricule).desc()).limit(5).all()

        total_aptes = SituationMedicale.query.filter_by(aptitude='apte').count()
        total_inaptes = personnel_inapte
        permissions_recentes = permissions_en_cours

        return render_template('rh/dashboard.html',
                             total_personnel=total_personnel,
                             stats_categories=stats_categories,
                             stats_grades=stats_grades,
                             stats_unites=stats_unites,
                             total_aptes=total_aptes,
                             total_inaptes=total_inaptes,
                             permissions_recentes=permissions_recentes,
                             detachements_en_cours=detachements_en_cours)
        
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('rh/dashboard.html',
                             total_personnel=0,
                             stats_categories=[],
                             stats_grades=[],
                             stats_unites=[],
                             total_aptes=0,
                             total_inaptes=0,
                             permissions_recentes=0,
                             detachements_en_cours=0)

# Route de recherche supprimée - utiliser celle dans rh_blueprint.py

# Fonction recherche_resultats supprimée - utiliser celle dans rh_blueprint.py

@rh_bp.route('/liste')
def liste_personnel():
    """Affichage de la liste complète du personnel"""
    try:
        # Pagination avec plus d'éléments par page
        page = request.args.get('page', 1, type=int)
        per_page = 100  # 100 militaires par page

        # Récupération avec pagination
        personnel_paginated = Personnel.query.join(Personnel.arme)\
                                           .join(Personnel.unite)\
                                           .join(Personnel.grade_actuel)\
                                           .join(Personnel.categorie)\
                                           .order_by(Personnel.nom, Personnel.prenom)\
                                           .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('rh/personnel/liste.html',
                             personnel=personnel_paginated.items,
                             pagination=personnel_paginated,
                             total=personnel_paginated.total)

    except Exception as e:
        flash(f'Erreur lors du chargement de la liste: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))

# Route fiche_personnel supprimée - utiliser celle dans rh_blueprint.py

# Route fiche_personnel_complete supprimée - utiliser celle dans rh_blueprint.py

# Routes d'ajout de militaire supprimées - utiliser celles dans rh_blueprint.py

# API pour récupérer les spécialités selon le service/arme sélectionné
@rh_bp.route('/api/specialites/<int:service_id>')
def get_specialites(service_id):
    """API pour récupérer les spécialités d'un service/arme"""
    try:
        specialites = ReferentielSpecialite.query.filter_by(id_arme=service_id).all()
        return jsonify([{'id': s.id_specialite, 'libelle': s.libelle} for s in specialites])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Route POST traiter_nouveau_militaire supprimée - utiliser celle dans rh_blueprint.py

# Code orphelin supprimé

@rh_bp.route('/init_rh_database')
def init_rh_database():
    """Route pour initialiser/réinitialiser la base de données RH"""
    try:
        # Cette route peut être utilisée pour des opérations de maintenance
        flash('Base de données RH initialisée avec succès!', 'success')
        return redirect(url_for('rh.dashboard'))
    except Exception as e:
        flash(f'Erreur lors de l\'initialisation: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))

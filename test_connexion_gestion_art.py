#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier la connexion à la base de données gestion-art
"""

import mysql.connector
from mysql.connector import Error
import sys
import os

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

def test_connexion_mysql():
    """Tester la connexion directe à MySQL"""
    print("🔌 Test de connexion directe à MySQL...")
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Vérifier la version de MySQL
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   ✅ Connexion réussie à MySQL {version[0]}")
            
            # Vérifier que la base de données existe
            cursor.execute("SHOW DATABASES LIKE 'gestion-art'")
            result = cursor.fetchone()
            
            if result:
                print(f"   ✅ Base de données 'gestion-art' trouvée")
                
                # Lister les tables existantes
                cursor.execute("USE `gestion-art`")
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                print(f"   📊 Tables trouvées ({len(tables)}):")
                for table in tables:
                    print(f"      - {table[0]}")
                    
                return True
            else:
                print("   ❌ Base de données 'gestion-art' non trouvée")
                print("   💡 Créez la base de données dans phpMyAdmin")
                return False
                
    except Error as e:
        print(f"   ❌ Erreur de connexion: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def test_connexion_flask():
    """Tester la connexion via Flask"""
    print("\n🌐 Test de connexion via Flask...")
    
    try:
        # Importer l'application Flask
        from art import app
        from db import db
        
        with app.app_context():
            # Tester la connexion SQLAlchemy
            from sqlalchemy import text
            result = db.session.execute(text("SELECT 1"))
            print("   ✅ Connexion SQLAlchemy réussie")
            
            # Vérifier les modèles
            from db import VehiculeGAR, CourrierArrive, CourrierEnvoye
            
            # Compter les enregistrements dans quelques tables
            try:
                vehicules_count = VehiculeGAR.query.count()
                print(f"   📊 VehiculeGAR: {vehicules_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table VehiculeGAR: {e}")
            
            try:
                courriers_arrives_count = CourrierArrive.query.count()
                print(f"   📊 CourrierArrive: {courriers_arrives_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table CourrierArrive: {e}")
            
            try:
                courriers_envoyes_count = CourrierEnvoye.query.count()
                print(f"   📊 CourrierEnvoye: {courriers_envoyes_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table CourrierEnvoye: {e}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur Flask: {e}")
        return False

def test_modeles_rh():
    """Tester les modèles RH"""
    print("\n👥 Test des modèles RH...")
    
    try:
        from art import app
        from rh_models import Personnel, ReferentielGrade, ReferentielUnite
        
        with app.app_context():
            try:
                personnel_count = Personnel.query.count()
                print(f"   📊 Personnel: {personnel_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table Personnel: {e}")
            
            try:
                grades_count = ReferentielGrade.query.count()
                print(f"   📊 ReferentielGrade: {grades_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table ReferentielGrade: {e}")
            
            try:
                unites_count = ReferentielUnite.query.count()
                print(f"   📊 ReferentielUnite: {unites_count} enregistrements")
            except Exception as e:
                print(f"   ⚠️ Table ReferentielUnite: {e}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur modèles RH: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST DE CONNEXION À LA BASE DE DONNÉES GESTION-ART")
    print("=" * 60)
    
    # Test 1: Connexion MySQL directe
    mysql_ok = test_connexion_mysql()
    
    if mysql_ok:
        # Test 2: Connexion Flask
        flask_ok = test_connexion_flask()
        
        # Test 3: Modèles RH
        rh_ok = test_modeles_rh()
        
        print("\n" + "=" * 60)
        print("📋 RÉSUMÉ DES TESTS:")
        print(f"   MySQL Direct: {'✅' if mysql_ok else '❌'}")
        print(f"   Flask/SQLAlchemy: {'✅' if flask_ok else '❌'}")
        print(f"   Modèles RH: {'✅' if rh_ok else '❌'}")
        
        if mysql_ok and flask_ok:
            print("\n🎉 CONNEXION RÉUSSIE!")
            print("💡 Vous pouvez maintenant lancer l'application:")
            print("   python art.py")
            print("   Puis aller sur: http://localhost:5000")
        else:
            print("\n❌ PROBLÈMES DÉTECTÉS")
            print("💡 Vérifiez la configuration de la base de données")
    else:
        print("\n❌ CONNEXION MYSQL ÉCHOUÉE")
        print("💡 Actions à effectuer:")
        print("   1. Vérifiez que MySQL est démarré")
        print("   2. Créez la base de données 'gestion-art' dans phpMyAdmin")
        print("   3. Vérifiez les paramètres de connexion")

if __name__ == "__main__":
    main()

# 🎯 Configuration de l'Application Gestion Art

## 📋 Modifications Effectuées

### 1. 🗄️ **Changement de Base de Données**
- **Ancienne base**: `gestion_vehicules`
- **Nouvelle base**: `gestion-art`
- **Fichiers modifiés**:
  - `db.py` : Configuration principale SQLAlchemy
  - `init_db.py` : Script d'initialisation
  - `insert_reference_data.py` : Insertion des données de référence
  - `create_vehicule_gar_table.py` : Création table véhicules GAR
  - `recreate_database.py` : Recréation de base
  - `restore_database.py` : Restauration de base
  - `reset_donnees_courriers.py` : Reset données courriers
  - `verify_tables.py` : Vérification des tables

### 2. 🚀 **Changement de Fichier Principal**
- **Ancien fichier**: `app.py`
- **Nouveau fichier**: `art.py`
- **Fichiers modifiés**:
  - `recreate_db.py` : Import corrigé
  - `mise_a_jour_simple.py` : Import corrigé
  - `test_affichage_complet.py` : Import corrigé
  - `test_application_web.py` : Import corrigé
  - `test_champs_affichage.py` : Import corrigé
  - `test_affichage_final.py` : Import corrigé
  - `add_realistic_situations.py` : Import corrigé
  - `create_personnel_simple.py` : Import corrigé

## 🛠️ **Installation et Configuration**

### Prérequis
- MySQL/MariaDB installé et démarré
- Python 3.x avec les dépendances installées
- phpMyAdmin (optionnel, pour gestion visuelle)

### Étape 1: Créer la Base de Données
```bash
# Option 1: Script automatique (recommandé)
python creer_base_gestion_art.py

# Option 2: Manuellement dans phpMyAdmin
# Créer une base de données nommée "gestion-art"
```

### Étape 2: Tester la Connexion
```bash
python test_connexion_gestion_art.py
```

### Étape 3: Lancer l'Application
```bash
python art.py
```

### Étape 4: Accéder à l'Interface
- **URL**: http://localhost:5000
- **Modules disponibles**:
  - Gestion des Véhicules
  - Gestion RH
  - Gestion des Stages
  - Gestion des Courriers

## 📊 **Structure de la Base de Données**

### Tables Principales
- **vehicule** : Véhicules en panne
- **vehicule_gar** : Référentiel des véhicules GAR
- **vehicule_historique** : Historique des changements
- **entretien** : Entretiens programmés

### Tables Courrier
- **courrier_arrive** : Courriers reçus
- **courrier_envoye** : Courriers envoyés
- **courrier_division_action** : Divisions d'action
- **courrier_division_info** : Divisions d'information

### Tables RH (25 tables)
- **Tables de référence** (10) : genres, grades, unités, etc.
- **Tables de données** (15) : personnel, famille, médical, etc.

### Tables Stages
- **stagiaire** : Informations des stagiaires
- **stage** : Types de stages
- **promotion** : Promotions de formation
- **inscription** : Inscriptions aux stages

## 🔧 **Configuration Technique**

### Paramètres de Connexion
```python
# Dans db.py
SQLALCHEMY_DATABASE_URI = 'mysql+mysqldb://root:@localhost/gestion-art'
```

### Port d'Écoute
```python
# Dans art.py
app.run(debug=True, host='0.0.0.0', port=5000)
```

### Authentification
- **Login classique** : username/password
- **QR Code** : Code spécifique pour utilisateur "redouane"
- **Reconnaissance faciale** : Implémentation de base

## 🚨 **Résolution de Problèmes**

### Erreur de Connexion MySQL
```bash
# Vérifier que MySQL est démarré
# Windows: services.msc → MySQL
# Linux: sudo systemctl status mysql

# Vérifier les paramètres de connexion
python test_connexion_gestion_art.py
```

### Base de Données Inexistante
```bash
# Créer automatiquement
python creer_base_gestion_art.py

# Ou manuellement dans phpMyAdmin
# CREATE DATABASE `gestion-art` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### Tables Manquantes
```bash
# Recréer toutes les tables
python creer_base_gestion_art.py

# Ou via Flask
from art import app
from db import db
with app.app_context():
    db.create_all()
```

### Erreurs d'Import
```bash
# Vérifier que tous les modules sont installés
pip install -r requirements.txt

# Vérifier la structure des fichiers
python -c "from art import app; print('Import OK')"
```

## 📈 **Fonctionnalités Principales**

### Module Véhicules
- Dashboard avec statistiques par GAR
- Gestion des pannes par type de véhicule
- Historique complet des réparations
- Recherche et filtrage avancés

### Module RH
- Gestion complète du personnel militaire
- Recherche multicritères
- Gestion famille et situation médicale
- Suivi des absences et mouvements

### Module Courrier
- Gestion courriers arrivés/envoyés
- Classification par urgence et nature
- Suivi par divisions
- Annotations et observations

### Module Stages
- Gestion des formations militaires
- Suivi des promotions
- Inscriptions et documents
- Rapports analytiques

## 🎉 **Prêt à Utiliser**

L'application est maintenant configurée pour utiliser la base de données `gestion-art` et peut être lancée avec `python art.py`. Toutes les fonctionnalités sont opérationnelles et l'interface est accessible sur http://localhost:5000.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour tester les nouvelles routes RH avec l'architecture complète
"""

import requests
import json
from datetime import date

# Configuration
BASE_URL = "http://localhost:5000"

def test_route_dashboard():
    """Tester la route dashboard RH"""
    print("🏠 TEST DASHBOARD RH")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/rh/")
        
        if response.status_code == 200:
            print("   ✅ Dashboard accessible")
            print(f"   📊 Taille réponse: {len(response.text)} caractères")
            
            # Vérifier que la page contient les éléments attendus
            if "Dashboard RH" in response.text or "Statistiques" in response.text:
                print("   ✅ Contenu dashboard détecté")
            else:
                print("   ⚠️ Contenu dashboard non détecté")
            
            return True
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_route_recherche():
    """Tester la route de recherche"""
    print("\n🔍 TEST RECHERCHE PERSONNEL")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/rh/recherche")
        
        if response.status_code == 200:
            print("   ✅ Page recherche accessible")
            print(f"   📊 Taille réponse: {len(response.text)} caractères")
            
            # Vérifier que la page contient les éléments de recherche
            if "recherche" in response.text.lower() or "personnel" in response.text.lower():
                print("   ✅ Contenu recherche détecté")
            else:
                print("   ⚠️ Contenu recherche non détecté")
            
            return True
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_recherche():
    """Tester l'API de recherche"""
    print("\n🔌 TEST API RECHERCHE")
    print("-" * 30)
    
    try:
        # Test avec recherche vide (tous les résultats)
        data = {}
        response = requests.post(f"{BASE_URL}/rh/api/recherche", 
                               json=data,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ API recherche fonctionnelle")
                print(f"   📊 {result.get('total', 0)} résultats trouvés")
                
                # Afficher quelques détails si des résultats existent
                personnel = result.get('personnel', [])
                if personnel:
                    premier = personnel[0]
                    print(f"   👤 Exemple: {premier.get('nom_complet', 'N/A')}")
                    print(f"   🎖️ Grade: {premier.get('grade', 'N/A')}")
                    print(f"   🏢 Unité: {premier.get('unite', 'N/A')}")
                
                return True
            else:
                print(f"   ❌ API retourne success=False: {result.get('error', 'Erreur inconnue')}")
                return False
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            print(f"   📝 Réponse: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_recherche_avec_criteres():
    """Tester l'API de recherche avec critères"""
    print("\n🎯 TEST API RECHERCHE AVEC CRITÈRES")
    print("-" * 30)
    
    try:
        # Test avec critère de recherche par nom
        data = {
            'search': 'TEST'  # Rechercher quelque chose qui n'existe probablement pas
        }
        response = requests.post(f"{BASE_URL}/rh/api/recherche", 
                               json=data,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Recherche avec critères fonctionnelle")
                print(f"   📊 {result.get('total', 0)} résultats pour 'TEST'")
                return True
            else:
                print(f"   ❌ Recherche échouée: {result.get('error', 'Erreur inconnue')}")
                return False
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_route_nouveau_militaire():
    """Tester la route nouveau militaire"""
    print("\n➕ TEST NOUVEAU MILITAIRE")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/rh/nouveau_militaire")
        
        if response.status_code == 200:
            print("   ✅ Page nouveau militaire accessible")
            print(f"   📊 Taille réponse: {len(response.text)} caractères")
            
            # Vérifier que la page contient un formulaire
            if "form" in response.text.lower() or "nouveau" in response.text.lower():
                print("   ✅ Formulaire détecté")
            else:
                print("   ⚠️ Formulaire non détecté")
            
            return True
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_route_fiche_inexistante():
    """Tester l'accès à une fiche personnel inexistante"""
    print("\n👤 TEST FICHE PERSONNEL INEXISTANTE")
    print("-" * 30)
    
    try:
        # Tester avec un ID qui n'existe probablement pas
        response = requests.get(f"{BASE_URL}/rh/personnel/99999")
        
        # Devrait rediriger ou afficher une erreur
        if response.status_code in [200, 302, 404]:
            print(f"   ✅ Gestion ID inexistant OK (HTTP {response.status_code})")
            return True
        else:
            print(f"   ⚠️ Réponse inattendue: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_connectivite_serveur():
    """Tester que le serveur Flask est accessible"""
    print("🌐 TEST CONNECTIVITÉ SERVEUR")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        
        if response.status_code == 200:
            print("   ✅ Serveur Flask accessible")
            return True
        else:
            print(f"   ❌ Serveur répond avec HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Impossible de se connecter au serveur")
        print("   💡 Vérifiez que l'application Flask est démarrée sur le port 5000")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TEST DES NOUVELLES ROUTES RH")
    print("Architecture complète (36 tables)")
    print("=" * 60)
    
    tests = [
        ("Connectivité serveur", test_connectivite_serveur),
        ("Dashboard RH", test_route_dashboard),
        ("Recherche personnel", test_route_recherche),
        ("API recherche", test_api_recherche),
        ("API recherche avec critères", test_api_recherche_avec_criteres),
        ("Nouveau militaire", test_route_nouveau_militaire),
        ("Fiche inexistante", test_route_fiche_inexistante)
    ]
    
    resultats = []
    
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
        except Exception as e:
            print(f"\n❌ ERREUR CRITIQUE dans {nom_test}: {e}")
            resultats.append((nom_test, False))
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS:")
    
    succes = 0
    for nom, resultat in resultats:
        status = "✅" if resultat else "❌"
        print(f"   {status} {nom}")
        if resultat:
            succes += 1
    
    print(f"\n📊 SCORE: {succes}/{len(tests)} tests réussis")
    
    if succes == len(tests):
        print("\n🎉 TOUTES LES ROUTES FONCTIONNENT!")
        print("💡 L'architecture complète est opérationnelle")
        print("🚀 Vous pouvez maintenant adapter les templates")
    elif succes >= len(tests) - 2:
        print("\n✅ LA PLUPART DES ROUTES FONCTIONNENT")
        print("🔧 Quelques ajustements mineurs peuvent être nécessaires")
    else:
        print(f"\n⚠️ {len(tests) - succes} tests échoués")
        print("🔧 Vérifiez les erreurs avant de continuer")

if __name__ == "__main__":
    main()

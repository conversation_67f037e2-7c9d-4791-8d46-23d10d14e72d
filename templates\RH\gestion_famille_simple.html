{% extends "rh/base_rh.html" %}

{% block title %}Modification Informations Conjoint - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-heart"></i>
                                Modification Informations Conjoint
                            </h2>
                            <small style="color: var(--text-light);">
                                {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }} {{ militaire.nom_complet }} - {{ militaire.matricule }}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de modification -->
    <form method="POST" action="{{ url_for('rh.modifier_famille_simple', matricule=militaire.matricule) }}">
        <!-- Section Personnel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations Personnel
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Adresse des parents -->
                            <div class="col-md-6 mb-3">
                                <label for="adresse_parents" class="form-label fw-bold">
                                    <i class="fas fa-home me-1"></i>Adresse des parents
                                </label>
                                <textarea class="form-control" id="adresse_parents" name="adresse_parents" rows="3" placeholder="Adresse complète des parents">{{ militaire.adresse_parents or '' }}</textarea>
                            </div>

                            <!-- État matrimonial -->
                            <div class="col-md-6 mb-3">
                                <label for="situation_familiale_id" class="form-label fw-bold">
                                    <i class="fas fa-heart me-1"></i>État matrimonial
                                </label>
                                <select class="form-select" id="situation_familiale_id" name="situation_familiale_id">
                                    <option value="">-- Sélectionner --</option>
                                    {% for etat in etats_matrimoniaux %}
                                    <option value="{{ etat.id }}" {% if militaire.situation_familiale_id == etat.id %}selected{% endif %}>
                                        {{ etat.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Conjoint -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-heart"></i>
                            Informations Conjoint
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Adresse conjoint -->
                            <div class="col-md-6 mb-3">
                                <label for="conjoint_adresse" class="form-label fw-bold">
                                    <i class="fas fa-map-marker-alt me-1"></i>Adresse
                                </label>
                                <textarea class="form-control" id="conjoint_adresse" name="conjoint_adresse" rows="2" placeholder="Adresse du conjoint">{{ conjoint.adresse if conjoint else '' }}</textarea>
                            </div>

                            <!-- Adresse conjoint (arabe) -->
                            <div class="col-md-6 mb-3">
                                <label for="conjoint_adresse_arabe" class="form-label fw-bold">
                                    <i class="fas fa-map-marker-alt me-1"></i>Adresse (arabe)
                                </label>
                                <textarea class="form-control" id="conjoint_adresse_arabe" name="conjoint_adresse_arabe" rows="2" placeholder="عنوان الزوج/الزوجة">{{ conjoint.adresse_arabe if conjoint else '' }}</textarea>
                            </div>

                            <!-- GSM conjoint -->
                            <div class="col-md-4 mb-3">
                                <label for="conjoint_gsm" class="form-label fw-bold">
                                    <i class="fas fa-phone me-1"></i>GSM
                                </label>
                                <input type="tel" class="form-control" id="conjoint_gsm" name="conjoint_gsm"
                                       placeholder="06XXXXXXXX" value="{{ conjoint.gsm if conjoint else '' }}">
                            </div>

                            <!-- Profession conjoint -->
                            <div class="col-md-4 mb-3">
                                <label for="conjoint_profession" class="form-label fw-bold">
                                    <i class="fas fa-briefcase me-1"></i>Profession
                                </label>
                                <input type="text" class="form-control" id="conjoint_profession" name="conjoint_profession"
                                       placeholder="Profession du conjoint" value="{{ conjoint.profession if conjoint else '' }}">
                            </div>

                            <!-- Profession conjoint (arabe) -->
                            <div class="col-md-4 mb-3">
                                <label for="conjoint_profession_arabe" class="form-label fw-bold">
                                    <i class="fas fa-briefcase me-1"></i>Profession (arabe)
                                </label>
                                <input type="text" class="form-control" id="conjoint_profession_arabe" name="conjoint_profession_arabe"
                                       placeholder="مهنة الزوج/الزوجة" value="{{ conjoint.profession_arabe if conjoint else '' }}">
                            </div>

                            <!-- Profession père conjoint -->
                            <div class="col-md-6 mb-3">
                                <label for="conjoint_profession_pere" class="form-label fw-bold">
                                    <i class="fas fa-male me-1"></i>Profession père
                                </label>
                                <input type="text" class="form-control" id="conjoint_profession_pere" name="conjoint_profession_pere"
                                       placeholder="Profession du père du conjoint" value="{{ conjoint.profession_pere if conjoint else '' }}">
                            </div>

                            <!-- Profession mère conjoint -->
                            <div class="col-md-6 mb-3">
                                <label for="conjoint_profession_mere" class="form-label fw-bold">
                                    <i class="fas fa-female me-1"></i>Profession mère
                                </label>
                                <input type="text" class="form-control" id="conjoint_profession_mere" name="conjoint_profession_mere"
                                       placeholder="Profession de la mère du conjoint" value="{{ conjoint.profession_mere if conjoint else '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>




        <!-- Boutons d'action -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary btn-lg me-3">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-success-military btn-lg">
                            <i class="fas fa-save me-2"></i>Enregistrer les modifications
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('form');

    if (form) {
        form.addEventListener('submit', function(e) {
            const nombreEnfants = document.getElementById('nombre_enfants').value;

        if (nombreEnfants && (nombreEnfants < 0 || nombreEnfants > 20)) {
            e.preventDefault();
            alert('Le nombre d\'enfants doit être entre 0 et 20.');
            return false;
        }

        // Validation des champs enfant si remplis
        const enfantNom = document.getElementById('enfant_nom_complet').value.trim();
        const enfantSexe = document.getElementById('enfant_sexe_id').value;
        const enfantDateNaissance = document.getElementById('enfant_date_naissance').value;
        const enfantDateDeces = document.getElementById('enfant_date_deces').value;

        if (enfantNom || enfantSexe || enfantDateNaissance || enfantDateDeces) {
            if (!enfantNom) {
                e.preventDefault();
                alert('Le nom complet de l\'enfant est obligatoire.');
                return false;
            }
            if (!enfantSexe) {
                e.preventDefault();
                alert('Le sexe de l\'enfant est obligatoire.');
                return false;
            }
            if (!enfantDateNaissance) {
                e.preventDefault();
                alert('La date de naissance de l\'enfant est obligatoire.');
                return false;
            }

            // Validation de la date de décès
            if (enfantDateDeces && enfantDateNaissance) {
                const dateNaissance = new Date(enfantDateNaissance);
                const dateDeces = new Date(enfantDateDeces);

                if (dateDeces <= dateNaissance) {
                    e.preventDefault();
                    alert('La date de décès doit être postérieure à la date de naissance.');
                    return false;
                }
            }
        }
    });

    // Calcul automatique de l'âge de l'enfant
    const enfantDateNaissance = document.getElementById('enfant_date_naissance');
    if (enfantDateNaissance) {
        enfantDateNaissance.addEventListener('change', function() {
            if (this.value) {
                const dateNaissance = new Date(this.value);
                const aujourd_hui = new Date();
                let age = aujourd_hui.getFullYear() - dateNaissance.getFullYear();
                const moisDiff = aujourd_hui.getMonth() - dateNaissance.getMonth();

                if (moisDiff < 0 || (moisDiff === 0 && aujourd_hui.getDate() < dateNaissance.getDate())) {
                    age--;
                }

                // Afficher l'âge à côté du champ (optionnel)
                let ageDisplay = document.getElementById('age-display');
                if (!ageDisplay) {
                    ageDisplay = document.createElement('small');
                    ageDisplay.id = 'age-display';
                    ageDisplay.className = 'text-muted ms-2';
                    this.parentNode.appendChild(ageDisplay);
                }
                ageDisplay.textContent = `(${age} ans)`;
            }
        });
    }

    // Validation du format GSM
    const gsmInput = document.getElementById('conjoint_gsm');
    if (gsmInput) {
        gsmInput.addEventListener('input', function() {
            // Supprimer tous les caractères non numériques
            this.value = this.value.replace(/[^0-9]/g, '');

            // Limiter à 10 chiffres
            if (this.value.length > 10) {
                this.value = this.value.substring(0, 10);
            }
        });
    }
});
</script>
{% endblock %}

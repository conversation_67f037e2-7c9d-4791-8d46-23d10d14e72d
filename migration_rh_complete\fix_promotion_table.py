#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour supprimer la table promotion existante avant recréation
"""

import mysql.connector
from mysql.connector import Error

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

def supprimer_table_promotion():
    """Supprimer la table promotion existante"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()

        # Désactiver les contraintes FK temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        print("⚠️ Contraintes FK désactivées")

        # Supprimer la table promotion si elle existe
        cursor.execute("DROP TABLE IF EXISTS promotion")
        print("✅ Table 'promotion' supprimée")

        # Réactiver les contraintes FK
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        print("✅ Contraintes FK réactivées")

        connection.commit()
        return True

    except Error as e:
        print(f"❌ Erreur: {e}")
        return False

    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🔧 Suppression de la table promotion existante...")
    supprimer_table_promotion()

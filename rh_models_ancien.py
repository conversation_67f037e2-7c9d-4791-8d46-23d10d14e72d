#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèles SQLAlchemy pour les tables RH selon architecture_rh.md
25 tables : 10 référentiels + 15 données
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from db import db

# ============================================================================
# TABLES DE RÉFÉRENCE (10 tables)
# ============================================================================

class ReferentielGenre(db.Model):
    __tablename__ = 'referentiel_genre'
    
    id_genre = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='genre', lazy=True)
    enfants = db.relationship('Enfant', backref='genre', lazy=True)

class ReferentielCategorie(db.Model):
    __tablename__ = 'referentiel_categorie'
    
    id_categorie = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='categorie', lazy=True)

class ReferentielGroupeSanguin(db.Model):
    __tablename__ = 'referentiel_groupe_sanguin'
    
    id_groupe = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(3), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='groupe_sanguin', lazy=True)

class ReferentielArme(db.Model):
    __tablename__ = 'referentiel_arme'
    
    id_arme = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False)
    
    # Relations
    specialites = db.relationship('ReferentielSpecialite', backref='arme', lazy=True)
    personnel = db.relationship('Personnel', backref='arme', lazy=True)
    mutations = db.relationship('MutationFonction', backref='service', lazy=True)

class ReferentielSpecialite(db.Model):
    __tablename__ = 'referentiel_specialite'
    
    id_specialite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    id_arme = db.Column(db.Integer, db.ForeignKey('referentiel_arme.id_arme'), nullable=False)
    libelle = db.Column(db.String(50), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='specialite', lazy=True)

class ReferentielUnite(db.Model):
    __tablename__ = 'referentiel_unite'
    
    id_unite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(100), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='unite', lazy=True)
    sejours_ops = db.relationship('SejourOps', backref='unite', lazy=True)

class ReferentielGrade(db.Model):
    __tablename__ = 'referentiel_grade'
    
    id_grade = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='grade_actuel', lazy=True)
    avancements_precedent = db.relationship('Avancement', foreign_keys='Avancement.grade_precedent_id', backref='grade_precedent', lazy=True)
    avancements_suivant = db.relationship('Avancement', foreign_keys='Avancement.grade_suivant_id', backref='grade_suivant', lazy=True)

class ReferentielSituationFamiliale(db.Model):
    __tablename__ = 'referentiel_situation_familiale'
    
    id_sitfam = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='situation_familiale', lazy=True)

class ReferentielDegreParente(db.Model):
    __tablename__ = 'referentiel_degre_parente'
    
    id_degre = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False)
    
    # Relations
    personnel = db.relationship('Personnel', backref='degre_parente', lazy=True)

class ReferentielLangue(db.Model):
    __tablename__ = 'referentiel_langue'
    
    id_langue = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False)
    
    # Relations
    personnel_langues = db.relationship('PersonnelLangue', backref='langue', lazy=True)

# ============================================================================
# TABLES DE DONNÉES (15 tables)
# ============================================================================

class Personnel(db.Model):
    __tablename__ = 'personnel'
    
    # Clé primaire
    matricule = db.Column(db.String(20), primary_key=True)
    
    # Informations personnelles
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    nom_arabe = db.Column(db.String(100), nullable=False)
    prenom_arabe = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    
    # Références
    sexe_id = db.Column(db.Integer, db.ForeignKey('referentiel_genre.id_genre'), nullable=False)
    categorie_id = db.Column(db.Integer, db.ForeignKey('referentiel_categorie.id_categorie'), nullable=False)
    groupe_sanguin_id = db.Column(db.Integer, db.ForeignKey('referentiel_groupe_sanguin.id_groupe'), nullable=False)
    
    # Documents
    numero_cin = db.Column(db.String(20), nullable=False)
    date_delivrance_cin = db.Column(db.Date, nullable=False)
    date_expiration_cin = db.Column(db.Date, nullable=False)
    
    # Contact
    gsm = db.Column(db.String(20), nullable=False)
    telephone_domicile = db.Column(db.String(20), nullable=True)
    
    # Physique
    taille = db.Column(db.Numeric(5,2), nullable=False)
    lieu_residence = db.Column(db.String(150), nullable=False)
    
    # Militaire
    arme_id = db.Column(db.Integer, db.ForeignKey('referentiel_arme.id_arme'), nullable=False)
    specialite_id = db.Column(db.Integer, db.ForeignKey('referentiel_specialite.id_specialite'), nullable=True)
    unite_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    grade_actuel_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    fonction = db.Column(db.String(100), nullable=False)
    date_prise_fonction = db.Column(db.Date, nullable=False)
    
    # Financier
    ccp = db.Column(db.String(50), nullable=False)
    compte_bancaire = db.Column(db.String(50), nullable=True)
    numero_somme = db.Column(db.String(50), nullable=False)
    date_engagement = db.Column(db.Date, nullable=False)
    
    # Famille
    nom_pere = db.Column(db.String(100), nullable=False)
    prenom_pere = db.Column(db.String(100), nullable=False)
    nom_mere = db.Column(db.String(100), nullable=False)
    prenom_mere = db.Column(db.String(100), nullable=False)
    adresse_parents = db.Column(db.String(200), nullable=False)
    situation_fam_id = db.Column(db.Integer, db.ForeignKey('referentiel_situation_familiale.id_sitfam'), nullable=False)
    nombre_enfants = db.Column(db.Integer, nullable=True)
    
    # Passeport (optionnel)
    numero_passport = db.Column(db.String(50), nullable=True)
    date_delivrance_passport = db.Column(db.Date, nullable=True)
    date_expiration_passport = db.Column(db.Date, nullable=True)
    
    # Urgence
    gsm_urgence = db.Column(db.String(20), nullable=False)
    degre_parente_id = db.Column(db.Integer, db.ForeignKey('referentiel_degre_parente.id_degre'), nullable=False)
    
    # Relations
    langues = db.relationship('PersonnelLangue', backref='personnel', lazy=True, cascade='all, delete-orphan')
    conjoint = db.relationship('Conjoint', backref='personnel', uselist=False, lazy=True, cascade='all, delete-orphan')
    enfants = db.relationship('Enfant', backref='personnel', lazy=True, cascade='all, delete-orphan')
    situation_medicale = db.relationship('SituationMedicale', backref='personnel', uselist=False, lazy=True, cascade='all, delete-orphan')
    vaccinations = db.relationship('Vaccination', backref='personnel', lazy=True, cascade='all, delete-orphan')
    ptcs = db.relationship('Ptc', backref='personnel', lazy=True, cascade='all, delete-orphan')
    permissions = db.relationship('Permission', backref='personnel', lazy=True, cascade='all, delete-orphan')
    desertions = db.relationship('Desertion', backref='personnel', lazy=True, cascade='all, delete-orphan')
    detachements = db.relationship('Detachement', backref='personnel', lazy=True, cascade='all, delete-orphan')
    mutations = db.relationship('MutationFonction', backref='personnel', lazy=True, cascade='all, delete-orphan')
    sejours_ops = db.relationship('SejourOps', backref='personnel', lazy=True, cascade='all, delete-orphan')
    liberations = db.relationship('Liberation', backref='personnel', lazy=True, cascade='all, delete-orphan')
    avancements = db.relationship('Avancement', backref='personnel', lazy=True, cascade='all, delete-orphan')
    sanctions = db.relationship('Sanction', backref='personnel', lazy=True, cascade='all, delete-orphan')
    
    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"
    
    @property
    def nom_complet_arabe(self):
        return f"{self.prenom_arabe} {self.nom_arabe}"

class PersonnelLangue(db.Model):
    __tablename__ = 'personnel_langue'

    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), primary_key=True)
    langue_id = db.Column(db.Integer, db.ForeignKey('referentiel_langue.id_langue'), primary_key=True)

class Conjoint(db.Model):
    __tablename__ = 'conjoint'

    id_conjoint = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False, unique=True)

    # Informations personnelles
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    nom_arabe = db.Column(db.String(100), nullable=False)
    prenom_arabe = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    lieu_naissance_arabe = db.Column(db.String(100), nullable=False)

    # Adresse
    adresse = db.Column(db.String(200), nullable=False)
    adresse_arabe = db.Column(db.String(200), nullable=False)

    # Mariage
    date_mariage = db.Column(db.Date, nullable=False)
    lieu_mariage = db.Column(db.String(100), nullable=False)

    # Profession
    profession = db.Column(db.String(100), nullable=False)
    profession_arabe = db.Column(db.String(100), nullable=False)

    # Documents et contact
    numero_cin = db.Column(db.String(20), nullable=False)
    gsm = db.Column(db.String(20), nullable=False)

    # Parents
    nom_pere = db.Column(db.String(100), nullable=False)
    prenom_pere = db.Column(db.String(100), nullable=False)
    nom_arabe_pere = db.Column(db.String(100), nullable=False)
    prenom_arabe_pere = db.Column(db.String(100), nullable=False)
    nom_mere = db.Column(db.String(100), nullable=False)
    prenom_mere = db.Column(db.String(100), nullable=False)
    nom_arabe_mere = db.Column(db.String(100), nullable=False)
    prenom_arabe_mere = db.Column(db.String(100), nullable=False)
    profession_pere = db.Column(db.String(100), nullable=False)
    profession_mere = db.Column(db.String(100), nullable=False)

    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"

    @property
    def nom_complet_arabe(self):
        return f"{self.prenom_arabe} {self.nom_arabe}"

class Enfant(db.Model):
    __tablename__ = 'enfant'

    id_enfant = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    sexe_id = db.Column(db.Integer, db.ForeignKey('referentiel_genre.id_genre'), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    date_deces = db.Column(db.Date, nullable=True)

    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"

    @property
    def est_vivant(self):
        return self.date_deces is None

class SituationMedicale(db.Model):
    __tablename__ = 'situation_medicale'

    id_sitmed = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False, unique=True)
    maladies = db.Column(db.Text, nullable=False)
    date_hospitalisation = db.Column(db.Date, nullable=False)
    lieu_hospitalisation = db.Column(db.String(100), nullable=False)
    aptitude = db.Column(db.Enum('apte', 'inapte'), nullable=False)
    observations = db.Column(db.Text, nullable=True)

class Vaccination(db.Model):
    __tablename__ = 'vaccination'

    id_vaccination = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_vaccination = db.Column(db.Date, nullable=False)
    objet = db.Column(db.String(100), nullable=False)
    observation = db.Column(db.Text, nullable=True)

class Ptc(db.Model):
    __tablename__ = 'ptc'

    id_ptc = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_ptc = db.Column(db.Date, nullable=False)
    duree = db.Column(db.Integer, nullable=False)  # en jours
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    objet = db.Column(db.String(100), nullable=False)
    observations = db.Column(db.Text, nullable=True)

class Permission(db.Model):
    __tablename__ = 'permission'

    id_permission = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    adresse = db.Column(db.String(200), nullable=False)
    numero_serie = db.Column(db.String(50), nullable=False)

class Desertion(db.Model):
    __tablename__ = 'desertion'

    id_desertion = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_absence = db.Column(db.Date, nullable=False)
    date_desertion = db.Column(db.Date, nullable=False)
    date_retour = db.Column(db.Date, nullable=False)
    date_arret_solde = db.Column(db.Date, nullable=True)
    date_prise_solde = db.Column(db.Date, nullable=True)

class Detachement(db.Model):
    __tablename__ = 'detachement'

    id_detachement = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    adresse_detachement = db.Column(db.String(200), nullable=False)
    pays = db.Column(db.String(100), nullable=False)
    date_fin = db.Column(db.Date, nullable=False)

class MutationFonction(db.Model):
    __tablename__ = 'mutation_fonction'

    id_mutation = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('referentiel_arme.id_arme'), nullable=False)
    fonction = db.Column(db.String(100), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)

class SejourOps(db.Model):
    __tablename__ = 'sejour_ops'

    id_sejour_ops = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    unite_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)

class Liberation(db.Model):
    __tablename__ = 'liberation'

    id_liberation = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    motif = db.Column(db.String(200), nullable=False)
    date_liberation = db.Column(db.Date, nullable=False)
    observation = db.Column(db.Text, nullable=True)

class Avancement(db.Model):
    __tablename__ = 'avancement'

    id_avancement = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    grade_precedent_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    grade_suivant_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    date_avancement = db.Column(db.Date, nullable=False)
    conditions = db.Column(db.Text, nullable=True)

class Sanction(db.Model):
    __tablename__ = 'sanction'

    id_sanction = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_sanction = db.Column(db.Date, nullable=False)
    type_sanction = db.Column(db.Enum('sanction', 'punition'), nullable=False)
    duree = db.Column(db.Integer, nullable=True)  # en jours
    motif = db.Column(db.String(200), nullable=False)
    observation = db.Column(db.Text, nullable=True)

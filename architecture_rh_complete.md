# Architecture de la base de données RH militaire

Ce document décrit en détail l’ensemble des tables, des relations et le script SQL de création de la base de données RH militaire, conformément au diagramme UML fourni.

---

## 1. Tables de référence (valeurs fixes)

### 1.1 `referentiel_type_sanction`
- **id_type_sanction** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'blâme'  
    - 'avertissement'  
    - 'arrêt simple'  
    - 'arrêt de rigueur'  
    - 'jugement'  

### 1.2 `referentiel_type_mvt`
- **id_type_mvt** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'détachement'  
    - 'mutation'  
    - 'engagement'  

### 1.3 `referentiel_origine`
- **id_origine** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles : 'ARM', 'ERART', 'HDT'  

### 1.4 `referentiel_lieu_medical`
- **id_lieu_medical** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  

### 1.5 `referentiel_etat_medical`
- **id_etat_medical** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(20) NOT NULL  
  - Valeurs possibles :  
    - 'Présent'  
    - 'Hors arme'  
    - 'Absent'  

### 1.6 `referentiel_type_medical`
- **id_type_medical** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'consultation'  
    - 'hospitalisation'  
    - 'vaccin'  
    - 'test psychotechnique'  

### 1.7 `referentiel_code_maladie`
- **id_code_maladie** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  

### 1.8 `referentiel_situation_familiale`
- **id_situation_familiale** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(20) NOT NULL  
  - Valeurs possibles :  
    - 'célibataire'  
    - 'marié'  
    - 'veuve'  

### 1.9 `referentiel_type_permission`
- **id_type_permission** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'normale nationale'  
    - 'normale étranger'  
    - 'exceptionnelle'  
    - 'congé maternité'  
    - 'PTC'  

### 1.10 `referentiel_type_absence`
- **id_type_absence** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'Désertion'  
    - 'libération'  
    - 'disparition'  
    - 'Décès'  

### 1.11 `referentiel_type_fonction`
- **id_type_fonction** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
- **bulle_condi** TEXT NULL  

### 1.12 `referentiel_type_note`
- **id_type_note** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(50) NOT NULL  
  - Valeurs possibles :  
    - 'Annuelle'  
    - 'Mutation'  
    - 'Fonction'  

### 1.13 `referentiel_categorie_grade`
- **id_categorie_grade** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
  - Valeurs possibles :  
    - 'Officier Général'  
    - 'Officier Supérieur'  
    - 'Officier Subalternes'  
    - 'ODR'  
    - 'MDR'  

---

## 2. Tables de données

### 2.1 `Ville`
- **id_ville** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
- **province** VARCHAR(100) NULL  
- **designation_arabe** VARCHAR(100) NULL  

### 2.2 `Personne`
- **id_pers** INT AUTO_INCREMENT PRIMARY KEY  
- **nom** VARCHAR(100) NOT NULL  
- **prenom** VARCHAR(100) NOT NULL  
- **nom_arabe** VARCHAR(100) NOT NULL  
- **prenom_arabe** VARCHAR(100) NOT NULL  
- **date_naissance** DATE NOT NULL  
- **date_deces** DATE NULL  
- **email** VARCHAR(150) NULL  
- **tel** VARCHAR(50) NULL  
- **scan_image** BLOB NULL  
- **surnom** VARCHAR(100) NULL  
- **num_passport** VARCHAR(50) NULL  
- **num_cine** VARCHAR(50) NULL  
- **scan_cine** BLOB NULL  
- **scan_passport** BLOB NULL  
- **scan_acte_nais** BLOB NULL  
- **gpe_sanguin** VARCHAR(10) NULL  

### 2.3 `Adresse`
- **id_adresse** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(200) NOT NULL  
- **designation_arabe** VARCHAR(200) NULL  
- **id_ville** INT NOT NULL → FOREIGN KEY `Ville(id_ville)`  
- **id_pers** INT NOT NULL → FOREIGN KEY `Personne(id_pers)`  

### 2.4 `Conjointe`
- **id_conjointe** INT PRIMARY KEY → FOREIGN KEY `Personne(id_pers)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  
- **scan_acte_mariage** BLOB NULL  

*(Relation 1 Personne—0..* Conjointe et 1 Militaire—0..* Conjointe)*

### 2.5 `Enfant`
- **id_enfant** INT PRIMARY KEY → FOREIGN KEY `Personne(id_pers)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  
- **sexe** VARCHAR(10) NOT NULL  

*(Relation 1 Personne—0..* Enfant et 1 Militaire—0..* Enfant)*

### 2.6 `Arm`
- **id_arm** INT AUTO_INCREMENT PRIMARY KEY  
- **specialite** VARCHAR(100) NULL  
- **designation** VARCHAR(100) NOT NULL  

### 2.7 `Unite`
- **id_organe** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
- **id_arm** INT NULL → FOREIGN KEY `Arm(id_arm)`  

*(Relation 1 Arm—1 Unite, 1 Organe peut n’avoir aucun ou plusieurs sous-unités)*

### 2.8 `Sous_unite`
- **id_sous_unite** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
- **id_organe** INT NOT NULL → FOREIGN KEY `Unite(id_organe)`  

*(Relation 1 Unite—0..* Sous_unite)*

### 2.9 `referentiel_grade` (table dynamique)
- **id_grade** INT AUTO_INCREMENT PRIMARY KEY  
- **designation** VARCHAR(100) NOT NULL  
- **id_categorie_grade** INT NOT NULL → FOREIGN KEY `referentiel_categorie_grade(id_categorie_grade)`  

### 2.10 `Militaire`
- **id_e_mili** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL UNIQUE  
- **num_mutuel** VARCHAR(50) NULL  
- **origine_id** INT NOT NULL → FOREIGN KEY `referentiel_origine(id_origine)`  
- **statut** VARCHAR(50) NULL  
- **date_engagement** DATE NOT NULL  
- **situation_fam_id** INT NOT NULL → FOREIGN KEY `referentiel_situation_familiale(id_situation_familiale)`  
- **fonction** VARCHAR(100) NULL  

*(Relation 1 Militaire—0..* Fonction, Mouvement, Permission, Absence, Punition, Jugement, 
Medical, Promotion, Evenement, Permanence, Tache)*

### 2.11 `Fonction`
- **id_fonction** INT AUTO_INCREMENT PRIMARY KEY  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NULL  
- **reference** VARCHAR(100) NULL  
- **decision** VARCHAR(100) NULL  
- **date_decision** DATE NULL  
- **id_type_fonction** INT NOT NULL → FOREIGN KEY `referentiel_type_fonction(id_type_fonction)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.12 `Notation`
- **id_note** INT AUTO_INCREMENT PRIMARY KEY  
- **note** VARCHAR(200) NULL  
- **date** DATE NULL  
- **tenue** VARCHAR(100) NULL  
- **disponibilite** VARCHAR(50) NULL  
- **moralite** VARCHAR(50) NULL  
- **autorite** VARCHAR(50) NULL  
- **execution** VARCHAR(50) NULL  
- **qualification** VARCHAR(50) NULL  
- **adaptation** VARCHAR(50) NULL  
- **rendement** VARCHAR(50) NULL  
- **sit_medical** VARCHAR(50) NULL  
- **esprit_equipe** VARCHAR(50) NULL  
- **discipline** VARCHAR(50) NULL  
- **condi_physique** VARCHAR(50) NULL  
- **commandement** VARCHAR(50) NULL  
- **scan_note** BLOB NULL  
- **observation** TEXT NULL  
- **id_type_note** INT NOT NULL → FOREIGN KEY `referentiel_type_note(id_type_note)`  

### 2.13 `Mission`
- **id_mission** INT AUTO_INCREMENT PRIMARY KEY  
- **date_debut** DATE NULL  
- **date_fin** DATE NULL  
- **designation** VARCHAR(100) NULL  
- **reference** VARCHAR(100) NULL  
*(Relations à gérer via tables de liaison si nécessaire)*

### 2.14 `Mouvement`
- **id_mvt** INT AUTO_INCREMENT PRIMARY KEY  
- **date** DATE NOT NULL  
- **scan_demande** BLOB NULL  
- **reference** VARCHAR(100) NULL  
- **id_type_mvt** INT NOT NULL → FOREIGN KEY `referentiel_type_mvt(id_type_mvt)`  
- **origine_id** INT NOT NULL → FOREIGN KEY `referentiel_origine(id_origine)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.15 `Punition`
- **id_sanction** INT AUTO_INCREMENT PRIMARY KEY  
- **date** DATE NOT NULL  
- **motif** VARCHAR(200) NULL  
- **observation** TEXT NULL  
- **duree** INT NULL  
- **reference** VARCHAR(100) NULL  
- **id_type_sanction** INT NOT NULL → FOREIGN KEY `referentiel_type_sanction(id_type_sanction)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.16 `Jugement`
- **id_jugement** INT AUTO_INCREMENT PRIMARY KEY  
- **nom_tribunal** VARCHAR(100) NULL  
- **num_dossier** VARCHAR(50) NULL  
- **type_jugement** VARCHAR(50) NULL  
- **decision** VARCHAR(200) NULL  
- **reference** VARCHAR(100) NULL  
- **date_jugement** DATE NULL  
- **id_sanction** INT NULL → FOREIGN KEY `Punition(id_sanction)`  

### 2.17 `Medical`
- **id_medical** INT AUTO_INCREMENT PRIMARY KEY  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NULL  
- **duree** INT NULL  
- **reference** VARCHAR(100) NULL  
- **observation** TEXT NULL  
- **etat_id** INT NOT NULL → FOREIGN KEY `referentiel_etat_medical(id_etat_medical)`  
- **id_lieu_medical** INT NOT NULL → FOREIGN KEY `referentiel_lieu_medical(id_lieu_medical)`  
- **id_type_medical** INT NOT NULL → FOREIGN KEY `referentiel_type_medical(id_type_medical)`  
- **id_code_maladie** INT NULL → FOREIGN KEY `referentiel_code_maladie(id_code_maladie)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.18 `Permission`
- **id_permission** INT AUTO_INCREMENT PRIMARY KEY  
- **num_serie** BIGINT NOT NULL  
- **date_demande** DATETIME NOT NULL  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NOT NULL  
- **motif** VARCHAR(200) NULL  
- **approbation_chef** BOOLEAN NOT NULL  
- **adresse** VARCHAR(200) NULL  
- **scan_demande** BLOB NULL  
- **duree** INT NULL  
- **id_type_permission** INT NOT NULL → FOREIGN KEY `referentiel_type_permission(id_type_permission)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.19 `Absence`
- **id_absence** INT AUTO_INCREMENT PRIMARY KEY  
- **date_absence** DATE NOT NULL  
- **date_rejoint** DATE NULL  
- **motif** VARCHAR(200) NULL  
- **reference** VARCHAR(100) NULL  
- **lieu** VARCHAR(200) NULL  
- **id_type_absence** INT NOT NULL → FOREIGN KEY `referentiel_type_absence(id_type_absence)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.20 `Promotion`
- **id_promotion** INT AUTO_INCREMENT PRIMARY KEY  
- **date** DATE NOT NULL  
- **reference** VARCHAR(100) NULL  
- **type** VARCHAR(50) NULL  
- **observation** TEXT NULL  
- **id_grade_precedent** INT NOT NULL → FOREIGN KEY `referentiel_grade(id_grade)`  
- **id_grade_suivant** INT NOT NULL → FOREIGN KEY `referentiel_grade(id_grade)`  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.21 `Evenement`
- **id_evenement** INT AUTO_INCREMENT PRIMARY KEY  
- **objet** VARCHAR(200) NULL  
- **reference** VARCHAR(100) NULL  
- **date** DATE NULL  
- **scan_doc** BLOB NULL  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.22 `Permanence`
- **id_permanence** INT AUTO_INCREMENT PRIMARY KEY  
- **date** DATE NOT NULL  
- **tour_aid** VARCHAR(50) NULL  
- **tour** VARCHAR(50) NULL  
- **observation** TEXT NULL  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

### 2.23 `Tache`
- **id_tache** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(200) NULL  
- **observation** TEXT NULL  
- **etat** VARCHAR(50) NULL  
- **id_e_mili** INT NOT NULL → FOREIGN KEY `Militaire(id_e_mili)`  

---

## 3. Script SQL de création

```sql
-- Tables de référence
CREATE TABLE referentiel_type_sanction (
  id_type_sanction INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_type_mvt (
  id_type_mvt INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_origine (
  id_origine INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_lieu_medical (
  id_lieu_medical INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL
);

CREATE TABLE referentiel_etat_medical (
  id_etat_medical INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(20) NOT NULL
);

CREATE TABLE referentiel_type_medical (
  id_type_medical INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_code_maladie (
  id_code_maladie INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL
);

CREATE TABLE referentiel_situation_familiale (
  id_situation_familiale INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(20) NOT NULL
);

CREATE TABLE referentiel_type_permission (
  id_type_permission INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_type_absence (
  id_type_absence INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_type_fonction (
  id_type_fonction INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL,
  bulle_condi TEXT
);

CREATE TABLE referentiel_type_note (
  id_type_note INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(50) NOT NULL
);

CREATE TABLE referentiel_categorie_grade (
  id_categorie_grade INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL
);

-- Tables de données
CREATE TABLE Ville (
  id_ville INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL,
  province VARCHAR(100),
  designation_arabe VARCHAR(100)
);

CREATE TABLE Personne (
  id_pers INT AUTO_INCREMENT PRIMARY KEY,
  nom VARCHAR(100) NOT NULL,
  prenom VARCHAR(100) NOT NULL,
  nom_arabe VARCHAR(100) NOT NULL,
  prenom_arabe VARCHAR(100) NOT NULL,
  date_naissance DATE NOT NULL,
  date_deces DATE,
  email VARCHAR(150),
  tel VARCHAR(50),
  scan_image BLOB,
  surnom VARCHAR(100),
  num_passport VARCHAR(50),
  num_cine VARCHAR(50),
  scan_cine BLOB,
  scan_passport BLOB,
  scan_acte_nais BLOB,
  gpe_sanguin VARCHAR(10)
);

CREATE TABLE Adresse (
  id_adresse INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(200) NOT NULL,
  designation_arabe VARCHAR(200),
  id_ville INT NOT NULL,
  id_pers INT NOT NULL,
  FOREIGN KEY (id_ville) REFERENCES Ville(id_ville),
  FOREIGN KEY (id_pers) REFERENCES Personne(id_pers)
);

CREATE TABLE Conjointe (
  id_conjointe INT PRIMARY KEY,
  id_e_mili INT NOT NULL,
  scan_acte_mariage BLOB,
  FOREIGN KEY (id_conjointe) REFERENCES Personne(id_pers),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Enfant (
  id_enfant INT PRIMARY KEY,
  id_e_mili INT NOT NULL,
  sexe VARCHAR(10) NOT NULL,
  FOREIGN KEY (id_enfant) REFERENCES Personne(id_pers),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Arm (
  id_arm INT AUTO_INCREMENT PRIMARY KEY,
  specialite VARCHAR(100),
  designation VARCHAR(100) NOT NULL
);

CREATE TABLE Unite (
  id_organe INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL,
  id_arm INT,
  FOREIGN KEY (id_arm) REFERENCES Arm(id_arm)
);

CREATE TABLE Sous_unite (
  id_sous_unite INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL,
  id_organe	INT NOT NULL,
  FOREIGN KEY (id_organe) REFERENCES Unite(id_organe)
);

CREATE TABLE referentiel_grade (
  id_grade INT AUTO_INCREMENT PRIMARY KEY,
  designation VARCHAR(100) NOT NULL,
  id_categorie_grade INT NOT NULL,
  FOREIGN KEY (id_categorie_grade) REFERENCES referentiel_categorie_grade(id_categorie_grade)
);

CREATE TABLE Militaire (
  id_e_mili INT AUTO_INCREMENT PRIMARY KEY,
  matricule VARCHAR(20) NOT NULL UNIQUE,
  num_mutuel VARCHAR(50),
  origine_id INT NOT NULL,
  statut VARCHAR(50),
  date_engagement DATE NOT NULL,
  situation_fam_id INT NOT NULL,
  fonction VARCHAR(100),
  FOREIGN KEY (origine_id) REFERENCES referentiel_origine(id_origine),
  FOREIGN KEY (situation_fam_id) REFERENCES referentiel_situation_familiale(id_situation_familiale)
);

CREATE TABLE Fonction (
  id_fonction INT AUTO_INCREMENT PRIMARY KEY,
  date_debut DATE NOT NULL,
  date_fin DATE,
  reference VARCHAR(100),
  decision VARCHAR(100),
  date_decision DATE,
  id_type_fonction INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_type_fonction) REFERENCES referentiel_type_fonction(id_type_fonction),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Notation (
  id_note INT AUTO_INCREMENT PRIMARY KEY,
  note VARCHAR(200),
  date DATE,
  tenue VARCHAR(100),
  disponibilite VARCHAR(50),
  moralite VARCHAR(50),
  autorite VARCHAR(50),
  execution VARCHAR(50),
  qualification VARCHAR(50),
  adaptation VARCHAR(50),
  rendement VARCHAR(50),
  sit_medical VARCHAR(50),
  esprit_equipe VARCHAR(50),
  discipline VARCHAR(50),
  condi_physique VARCHAR(50),
  commandement VARCHAR(50),
  scan_note BLOB,
  observation TEXT,
  id_type_note INT NOT NULL,
  FOREIGN KEY (id_type_note) REFERENCES referentiel_type_note(id_type_note)
);

CREATE TABLE Mission (
  id_mission INT AUTO_INCREMENT PRIMARY KEY,
  date_debut DATE,
  date_fin DATE,
  designation VARCHAR(100),
  reference VARCHAR(100)
);

CREATE TABLE Mouvement (
  id_mvt INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  scan_demande BLOB,
  reference VARCHAR(100),
  id_type_mvt INT NOT NULL,
  origine_id INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_type_mvt) REFERENCES referentiel_type_mvt(id_type_mvt),
  FOREIGN KEY (origine_id) REFERENCES referentiel_origine(id_origine),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Punition (
  id_sanction INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  motif VARCHAR(200),
  observation TEXT,
  duree INT,
  reference VARCHAR(100),
  id_type_sanction INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_type_sanction) REFERENCES referentiel_type_sanction(id_type_sanction),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Jugement (
  id_jugement INT AUTO_INCREMENT PRIMARY KEY,
  nom_tribunal VARCHAR(100),
  num_dossier VARCHAR(50),
  type_jugement VARCHAR(50),
  decision VARCHAR(200),
  reference VARCHAR(100),
  date_jugement DATE,
  id_sanction INT,
  FOREIGN KEY (id_sanction) REFERENCES Punition(id_sanction)
);

CREATE TABLE Medical (
  id_medical INT AUTO_INCREMENT PRIMARY KEY,
  date_debut DATE NOT NULL,
  date_fin DATE,
  duree INT,
  reference VARCHAR(100),
  observation TEXT,
  etat_id INT NOT NULL,
  id_lieu_medical INT NOT NULL,
  id_type_medical INT NOT NULL,
  id_code_maladie INT,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (etat_id) REFERENCES referentiel_etat_medical(id_etat_medical),
  FOREIGN KEY (id_lieu_medical) REFERENCES referentiel_lieu_medical(id_lieu_medical),
  FOREIGN KEY (id_type_medical) REFERENCES referentiel_type_medical(id_type_medical),
  FOREIGN KEY (id_code_maladie) REFERENCES referentiel_code_maladie(id_code_maladie),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Permission (
  id_permission INT AUTO_INCREMENT PRIMARY KEY,
  num_serie BIGINT NOT NULL,
  date_demande DATETIME NOT NULL,
  date_debut DATE NOT NULL,
  date_fin DATE NOT NULL,
  motif VARCHAR(200),
  approbation_chef BOOLEAN NOT NULL,
  adresse VARCHAR(200),
  scan_demande BLOB,
  duree INT,
  id_type_permission INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_type_permission) REFERENCES referentiel_type_permission(id_type_permission),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Absence (
  id_absence INT AUTO_INCREMENT PRIMARY KEY,
  date_absence DATE NOT NULL,
  date_rejoint DATE,
  motif VARCHAR(200),
  reference VARCHAR(100),
  lieu VARCHAR(200),
  id_type_absence INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_type_absence) REFERENCES referentiel_type_absence(id_type_absence),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Promotion (
  id_promotion INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  reference VARCHAR(100),
  type VARCHAR(50),
  observation TEXT,
  id_grade_precedent INT NOT NULL,
  id_grade_suivant INT NOT NULL,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_grade_precedent) REFERENCES referentiel_grade(id_grade),
  FOREIGN KEY (id_grade_suivant) REFERENCES referentiel_grade(id_grade),
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Evenement (
  id_evenement INT AUTO_INCREMENT PRIMARY KEY,
  objet VARCHAR(200),
  reference VARCHAR(100),
  date DATE,
  scan_doc BLOB,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Permanence (
  id_permanence INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  tour_aid VARCHAR(50),
  tour VARCHAR(50),
  observation TEXT,
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);

CREATE TABLE Tache (
  id_tache INT AUTO_INCREMENT PRIMARY KEY,
  libelle VARCHAR(200),
  observation TEXT,
  etat VARCHAR(50),
  id_e_mili INT NOT NULL,
  FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili)
);
```
{% extends "rh/base_rh.html" %}

{% block title %}Recherche Personnel - Gestion RH{% endblock %}

{% block extra_css %}
<style>
    /* INTERFACE MODERNE POUR SÉLECTION DES CRITÈRES */
    .criteria-selector {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .criteria-selector:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .criteria-toggle-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(75, 83, 32, 0.3);
    }

    .criteria-toggle-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(75, 83, 32, 0.4);
        color: white;
    }

    .criteria-checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .criteria-checkbox-item {
        background: white;
        border-radius: 10px;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .criteria-checkbox-item:hover {
        border-color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .criteria-checkbox-item.active {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, rgba(75, 83, 32, 0.1), rgba(107, 142, 35, 0.1));
    }

    .criteria-checkbox-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .criteria-checkbox-item.active::before {
        transform: scaleX(1);
    }

    .criteria-checkbox {
        margin-right: 0.5rem;
        transform: scale(1.2);
        accent-color: var(--primary-color);
    }

    .criteria-label {
        font-weight: 500;
        color: var(--text-color);
        margin: 0;
        display: flex;
        align-items: center;
    }

    .criteria-icon {
        margin-right: 0.5rem;
        color: var(--primary-color);
        font-size: 1.1em;
    }

    .criteria-field {
        transition: all 0.3s ease;
        display: none !important;
    }

    .criteria-field.active {
        display: block !important;
        opacity: 1;
        transform: scale(1);
    }

    .preset-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .preset-btn {
        background: white;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .preset-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(75, 83, 32, 0.3);
    }

    /* STYLES ADDITIONNELS POUR LA TABLE ET LES ÉLÉMENTS */
    .table-row-hover {
        transition: all 0.3s ease;
    }

    .table-row-hover:hover {
        background-color: rgba(212, 175, 55, 0.1) !important;
        transform: scale(1.01);
    }

    .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
    }

    .badge {
        font-size: 0.75em;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-search"></i>
                                Recherche et Consultation du Personnel
                            </h2>
                            <small style="color: var(--text-light);">Recherche multicritères et consultation des fiches personnelles</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                                <i class="fas fa-user-plus"></i> Nouveau Militaire
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sélecteur de Critères Avancés -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="criteria-selector">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Personnaliser les Critères de Recherche
                    </h5>
                    <button type="button" class="criteria-toggle-btn" onclick="toggleCriteriaSelector()">
                        <i class="fas fa-sliders-h me-2"></i>
                        <span id="toggle-text">Configurer</span>
                    </button>
                </div>

                <div id="criteria-selector-content" style="display: none;">
                    <p class="text-muted mb-3">
                        <i class="fas fa-info-circle me-1"></i>
                        Sélectionnez les critères que vous souhaitez afficher dans la recherche avancée
                    </p>

                    <!-- Boutons de présélection -->
                    <div class="preset-buttons">
                        <button type="button" class="preset-btn" onclick="selectPreset('basic')">
                            <i class="fas fa-user me-1"></i>Basique
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('identification')">
                            <i class="fas fa-fingerprint me-1"></i>Identification
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('military')">
                            <i class="fas fa-medal me-1"></i>Militaire
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('personal')">
                            <i class="fas fa-id-card me-1"></i>Personnel
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('medical')">
                            <i class="fas fa-user-md me-1"></i>Médical
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('advanced')">
                            <i class="fas fa-cogs me-1"></i>Avancé
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('all')">
                            <i class="fas fa-check-double me-1"></i>Tout
                        </button>
                        <button type="button" class="preset-btn" onclick="selectPreset('none')">
                            <i class="fas fa-times me-1"></i>Aucun
                        </button>
                    </div>

                    <!-- Grille de sélection des critères -->
                    <div class="criteria-checkbox-group">
                        <!-- Critères d'identification -->
                        <div class="criteria-checkbox-item" data-category="identification" data-field="cin">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-cin">
                                <i class="fas fa-id-card criteria-icon"></i>
                                CIN
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="identification" data-field="gsm">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-gsm">
                                <i class="fas fa-mobile-alt criteria-icon"></i>
                                GSM
                            </label>
                        </div>

                        <!-- Critères militaires -->
                        <div class="criteria-checkbox-item" data-category="military" data-field="categorie">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-categorie">
                                <i class="fas fa-tags criteria-icon"></i>
                                Catégorie
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="military" data-field="service">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-service">
                                <i class="fas fa-shield-alt criteria-icon"></i>
                                Service/Arme
                            </label>
                        </div>

                        <!-- Critères personnels -->
                        <div class="criteria-checkbox-item" data-category="personal" data-field="genre">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-genre">
                                <i class="fas fa-venus-mars criteria-icon"></i>
                                Genre
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="personal" data-field="matrimonial">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-matrimonial">
                                <i class="fas fa-heart criteria-icon"></i>
                                État Matrimonial
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="personal" data-field="sanguin">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-sanguin">
                                <i class="fas fa-tint criteria-icon"></i>
                                Groupe Sanguin
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="personal" data-field="lieu">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-lieu">
                                <i class="fas fa-map-marker-alt criteria-icon"></i>
                                Lieu de Naissance
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="personal" data-field="fonction">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-fonction">
                                <i class="fas fa-briefcase criteria-icon"></i>
                                Fonction
                            </label>
                        </div>

                        <!-- Critères médicaux -->
                        <div class="criteria-checkbox-item" data-category="medical" data-field="aptitude">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-aptitude">
                                <i class="fas fa-user-md criteria-icon"></i>
                                Aptitude Médicale
                            </label>
                        </div>

                        <!-- Critères avancés -->
                        <div class="criteria-checkbox-item" data-category="advanced" data-field="age_min">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-age_min">
                                <i class="fas fa-sort-numeric-up criteria-icon"></i>
                                Âge Min/Max
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="advanced" data-field="tranche_age">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-tranche_age">
                                <i class="fas fa-birthday-cake criteria-icon"></i>
                                Tranche d'Âge
                            </label>
                        </div>

                        <div class="criteria-checkbox-item" data-category="advanced" data-field="taille">
                            <label class="criteria-label">
                                <input type="checkbox" class="criteria-checkbox" id="show-taille">
                                <i class="fas fa-ruler-vertical criteria-icon"></i>
                                Tranche de Taille
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Critères de Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <!-- RECHERCHE SIMPLE : 4 critères principaux sur une ligne -->
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Nom/Prénom</label>
                            <input type="text" name="search" class="form-control"
                                   placeholder="Rechercher par nom ou prénom..." value="{{ search_term or '' }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Matricule</label>
                            <input type="text" name="matricule" class="form-control"
                                   placeholder="Ex: 1236589..." value="{{ matricule }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Grade</label>
                            <select name="grade_id" class="form-select">
                                <option value="">Tous les grades</option>
                                {% if grades %}
                                    {% for grade in grades %}
                                    <option value="{{ grade.id_grade }}" {% if grade_id and grade.id_grade == grade_id %}selected{% endif %}>
                                        {{ grade.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Unité</label>
                            <select name="unite_id" class="form-select">
                                <option value="">Toutes les unités</option>
                                {% if unites %}
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}" {% if unite_id and unite.id_unite == unite_id %}selected{% endif %}>
                                        {{ unite.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        <!-- BOUTON RECHERCHE AVANCÉE -->
                        <div class="col-12 text-center mt-3">
                            <button type="button" class="btn btn-outline-primary" id="toggle-recherche-avancee">
                                <i class="fas fa-search-plus"></i> Recherche Avancée
                            </button>
                        </div>

                        <!-- SECTION RECHERCHE AVANCÉE (masquée par défaut) -->
                        <div class="col-12" id="recherche-avancee" style="display: none;">
                            <hr class="my-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-filter"></i> Critères Avancés
                            </h6>
                            <div class="row g-3">
                                <!-- Critères d'identification -->
                                <div class="col-md-3 criteria-field" data-field="cin">
                                    <label class="form-label fw-bold">CIN</label>
                                    <input type="text" name="cin" class="form-control"
                                           placeholder="Ex: D784517..." value="{{ cin or '' }}">
                                </div>
                                <div class="col-md-3 criteria-field" data-field="gsm">
                                    <label class="form-label fw-bold">GSM</label>
                                    <input type="text" name="gsm" class="form-control"
                                           placeholder="Ex: 0612345678..." value="{{ gsm or '' }}">
                                </div>

                                <!-- Critères militaires -->
                                <div class="col-md-3 criteria-field" data-field="categorie">
                                    <label class="form-label fw-bold">Catégorie</label>
                                    <select name="categorie_id" class="form-select">
                                        <option value="">Toutes les catégories</option>
                                        {% if categories %}
                                            {% for categorie in categories %}
                                            <option value="{{ categorie.id_categorie }}" {% if categorie_id and categorie.id_categorie == categorie_id %}selected{% endif %}>
                                                {{ categorie.libelle }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="service">
                                    <label class="form-label fw-bold">Service/Arme</label>
                                    <select name="service_id" class="form-select">
                                        <option value="">Tous les services</option>
                                        {% if services %}
                                            {% for service in services %}
                                            <option value="{{ service.id_arme }}" {% if service_id and service.id_arme == service_id %}selected{% endif %}>
                                                {{ service.libelle }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>

                                <!-- Critères personnels -->
                                <div class="col-md-3 criteria-field" data-field="genre">
                                    <label class="form-label fw-bold">Genre</label>
                                    <select name="genre_id" class="form-select">
                                        <option value="">Tous les genres</option>
                                        {% if genres %}
                                            {% for genre in genres %}
                                            <option value="{{ genre.id_genre }}" {% if genre_id and genre.id_genre == genre_id %}selected{% endif %}>
                                                {{ genre.libelle }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="matrimonial">
                                    <label class="form-label fw-bold">État Matrimonial</label>
                                    <select name="etat_matrimonial_id" class="form-select">
                                        <option value="">Tous les états</option>
                                        {% if etats_matrimoniaux %}
                                            {% for etat in etats_matrimoniaux %}
                                            <option value="{{ etat.id_sitfam }}" {% if etat_matrimonial_id and etat.id_sitfam == etat_matrimonial_id %}selected{% endif %}>
                                                {{ etat.libelle }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="sanguin">
                                    <label class="form-label fw-bold">Groupe Sanguin</label>
                                    <select name="groupe_sanguin_id" class="form-select">
                                        <option value="">Tous les groupes</option>
                                        {% if groupes_sanguins %}
                                            {% for groupe in groupes_sanguins %}
                                            <option value="{{ groupe.id_groupe }}" {% if groupe_sanguin_id and groupe.id_groupe == groupe_sanguin_id %}selected{% endif %}>
                                                {{ groupe.libelle }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="lieu">
                                    <label class="form-label fw-bold">Lieu de Naissance</label>
                                    <input type="text" name="lieu_naissance" class="form-control"
                                           placeholder="Ex: Rabat, Casablanca..." value="{{ lieu_naissance or '' }}">
                                </div>

                                <!-- Critères médicaux -->
                                <div class="col-md-3 criteria-field" data-field="aptitude">
                                    <label class="form-label fw-bold">Aptitude Médicale</label>
                                    <select name="aptitude" class="form-select">
                                        <option value="">Toutes les aptitudes</option>
                                        <option value="Apte" {% if aptitude == 'Apte' %}selected{% endif %}>Apte</option>
                                        <option value="Inapte" {% if aptitude == 'Inapte' %}selected{% endif %}>Inapte</option>
                                    </select>
                                </div>

                                <!-- Critères avancés supplémentaires -->
                                <div class="col-md-3 criteria-field" data-field="tranche_age">
                                    <label class="form-label fw-bold">Tranche d'Âge</label>
                                    <select name="tranche_age" class="form-select">
                                        <option value="">Tous les âges</option>
                                        <option value="18-25" {% if tranche_age == '18-25' %}selected{% endif %}>18-25 ans</option>
                                        <option value="26-35" {% if tranche_age == '26-35' %}selected{% endif %}>26-35 ans</option>
                                        <option value="36-45" {% if tranche_age == '36-45' %}selected{% endif %}>36-45 ans</option>
                                        <option value="46-55" {% if tranche_age == '46-55' %}selected{% endif %}>46-55 ans</option>
                                        <option value="56+" {% if tranche_age == '56+' %}selected{% endif %}>56+ ans</option>
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="taille">
                                    <label class="form-label fw-bold">Tranche de Taille</label>
                                    <select name="tranche_taille" class="form-select">
                                        <option value="">Toutes tailles</option>
                                        <option value="150-160" {% if tranche_taille == '150-160' %}selected{% endif %}>150-160 cm</option>
                                        <option value="161-170" {% if tranche_taille == '161-170' %}selected{% endif %}>161-170 cm</option>
                                        <option value="171-180" {% if tranche_taille == '171-180' %}selected{% endif %}>171-180 cm</option>
                                        <option value="181-190" {% if tranche_taille == '181-190' %}selected{% endif %}>181-190 cm</option>
                                        <option value="191+" {% if tranche_taille == '191+' %}selected{% endif %}>191+ cm</option>
                                    </select>
                                </div>
                                <div class="col-md-3 criteria-field" data-field="fonction">
                                    <label class="form-label fw-bold">Fonction</label>
                                    <input type="text" name="fonction" class="form-control"
                                           placeholder="Ex: Chef, Adjoint..." value="{{ fonction or '' }}">
                                </div>

                                <!-- Critères d'âge numérique -->
                                <div class="col-md-3 criteria-field" data-field="age_min">
                                    <label class="form-label fw-bold">Âge Minimum</label>
                                    <input type="number" name="age_min" class="form-control" min="18" max="65"
                                           placeholder="Ex: 25" value="{{ age_min or '' }}">
                                </div>
                                <div class="col-md-3 criteria-field" data-field="age_min">
                                    <label class="form-label fw-bold">Âge Maximum</label>
                                    <input type="number" name="age_max" class="form-control" min="18" max="65"
                                           placeholder="Ex: 45" value="{{ age_max or '' }}">
                                </div>
                            </div>
                        </div>





                        <!-- Boutons d'action -->
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-military btn-lg w-100">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-secondary btn-lg w-100">
                                <i class="fas fa-eraser"></i> Effacer
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats de la Recherche -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-users"></i>
                                Résultats de la Recherche
                                {% if personnel %}
                                <span class="badge bg-light text-dark ms-2">{{ personnel.total }} résultat(s)</span>
                                {% endif %}
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if alerte == 'cin' %}
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-exclamation-triangle"></i> CIN à renouveler
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if personnel and personnel.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom Complet</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Arme</th>
                                    <th>Fonction</th>
                                    <th>CIN</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for militaire in personnel.items %}
                                <tr class="table-row-hover">
                                    <td>
                                        <strong class="text-primary">{{ militaire.matricule }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ militaire.nom }} {{ militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ militaire.nom_arabe }} {{ militaire.prenom_arabe }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <small>{{ militaire.unite.libelle if militaire.unite else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ militaire.arme.libelle if militaire.arme else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ militaire.fonction[:30] }}{% if militaire.fonction and militaire.fonction|length > 30 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        <small>{{ militaire.numero_cin if militaire.numero_cin else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" 
                                           class="btn btn-info btn-sm" title="Voir la fiche complète">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if personnel.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination justify-content-center mb-0">
                                {% if personnel.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=personnel.prev_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                        <i class="fas fa-chevron-left"></i> Précédent
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for page_num in personnel.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != personnel.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=page_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if personnel.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=personnel.next_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                        Suivant <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun résultat trouvé</h5>
                        <p class="text-muted">
                            {% if search or matricule or grade_id or unite_id or service_id %}
                            Aucun militaire ne correspond aux critères de recherche.
                            {% else %}
                            Utilisez les filtres ci-dessus pour rechercher du personnel.
                            {% endif %}
                        </p>
                        <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                            <i class="fas fa-user-plus"></i> Ajouter un Nouveau Militaire
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



{% block extra_js %}
<script>
function modifierPersonnel(matricule) {
    alert('Modification du personnel ' + matricule + ' - Fonctionnalité en développement');
}

function ajouterInfo(matricule) {
    alert('Ajout d\'informations pour ' + matricule + ' - Fonctionnalité en développement');
}

// NOUVELLE FONCTION : Toggle pour la recherche avancée
function toggleAdvancedSearch() {
    // Liste COMPLÈTE des critères avancés SUPPLÉMENTAIRES
    const criteresAvances = [
        // Critères de base avancés
        'critere-age', 'critere-anciennete', 'critere-taille', 'critere-fonction',
        // Critères disciplinaires et opérationnels
        'critere-sanctions', 'critere-absences', 'critere-detachements', 'critere-mutations',
        // Critères médicaux et familiaux
        'critere-medical', 'critere-famille', 'critere-carriere', 'critere-operationnel',
        // Critères administratifs et alertes
        'critere-documents', 'critere-alertes', 'critere-performance'
    ];

    const btnText = document.getElementById('advanced-btn-text');
    const premierCritere = document.getElementById('critere-age');

    if (premierCritere) {
        const isHidden = premierCritere.style.display === 'none';

        if (isHidden) {
            // Afficher tous les critères avancés SUPPLÉMENTAIRES - AFFICHAGE INSTANTANÉ
            criteresAvances.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = 'block';
                    element.style.opacity = '1';
                }
            });
            btnText.textContent = 'Simple';
        } else {
            // Masquer tous les critères avancés SUPPLÉMENTAIRES - MASQUAGE INSTANTANÉ
            criteresAvances.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                }
            });
            btnText.textContent = 'Avancé';
        }
    }
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    // Masquer initialement TOUS les critères avancés supplémentaires
    const criteresAvances = [
        // Critères de base avancés
        'critere-age', 'critere-anciennete', 'critere-taille', 'critere-fonction',
        // Critères disciplinaires et opérationnels
        'critere-sanctions', 'critere-absences', 'critere-detachements', 'critere-mutations',
        // Critères médicaux et familiaux
        'critere-medical', 'critere-famille', 'critere-carriere', 'critere-operationnel',
        // Critères administratifs et alertes
        'critere-documents', 'critere-alertes', 'critere-performance'
    ];

    criteresAvances.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Animation des lignes du tableau
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});

// Toggle de la recherche avancée
document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggle-recherche-avancee');
    const rechercheAvancee = document.getElementById('recherche-avancee');

    if (toggleBtn && rechercheAvancee) {
        toggleBtn.addEventListener('click', function() {
            if (rechercheAvancee.style.display === 'none') {
                rechercheAvancee.style.display = 'block';
                toggleBtn.innerHTML = '<i class="fas fa-search-minus"></i> Masquer Recherche Avancée';
                toggleBtn.classList.remove('btn-outline-primary');
                toggleBtn.classList.add('btn-primary');
            } else {
                rechercheAvancee.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fas fa-search-plus"></i> Recherche Avancée';
                toggleBtn.classList.remove('btn-primary');
                toggleBtn.classList.add('btn-outline-primary');
            }
        });

        // Si des critères avancés sont présents, afficher la section
        const criteres = ['cin', 'gsm', 'categorie_id', 'service_id', 'genre_id', 'etat_matrimonial_id', 'groupe_sanguin_id', 'lieu_naissance', 'aptitude', 'tranche_age', 'tranche_taille', 'fonction'];
        let hasCriteres = false;

        criteres.forEach(function(critere) {
            const element = document.querySelector(`[name="${critere}"]`);
            if (element && element.value && element.value !== '') {
                hasCriteres = true;
            }
        });

        if (hasCriteres) {
            rechercheAvancee.style.display = 'block';
            toggleBtn.innerHTML = '<i class="fas fa-search-minus"></i> Masquer Recherche Avancée';
            toggleBtn.classList.remove('btn-outline-primary');
            toggleBtn.classList.add('btn-primary');
        }
    }
});

// GESTION DES CRITÈRES PERSONNALISABLES
function toggleCriteriaSelector() {
    const content = document.getElementById('criteria-selector-content');
    const toggleText = document.getElementById('toggle-text');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        toggleText.textContent = 'Masquer';
    } else {
        content.style.display = 'none';
        toggleText.textContent = 'Configurer';
    }
}

function selectPreset(preset) {
    const checkboxes = document.querySelectorAll('.criteria-checkbox');
    const items = document.querySelectorAll('.criteria-checkbox-item');

    // Définir les présets
    const presets = {
        'basic': [],
        'military': ['categorie', 'service'],
        'personal': ['genre', 'matrimonial', 'sanguin', 'lieu', 'fonction'],
        'identification': ['cin', 'gsm'],
        'medical': ['aptitude'],
        'advanced': ['age_min', 'tranche_age', 'taille'],
        'all': ['cin', 'gsm', 'categorie', 'service', 'genre', 'matrimonial', 'sanguin', 'lieu', 'fonction', 'aptitude', 'age_min', 'tranche_age', 'taille'],
        'none': []
    };

    const selectedFields = presets[preset] || [];

    checkboxes.forEach(checkbox => {
        const field = checkbox.id.replace('show-', '');
        checkbox.checked = selectedFields.includes(field);
        updateCriteriaField(field, checkbox.checked);
    });

    // Mettre à jour l'apparence des items
    items.forEach(item => {
        const field = item.dataset.field;
        if (selectedFields.includes(field)) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });

    // Sauvegarder l'état après sélection du preset
    saveCriteriaState();
}

function updateCriteriaField(field, show) {
    // Chercher tous les éléments avec ce data-field (il peut y en avoir plusieurs)
    const fieldElements = document.querySelectorAll(`[data-field="${field}"]`);

    fieldElements.forEach(fieldElement => {
        if (show) {
            fieldElement.classList.add('active');
            console.log(`Activé: ${field}`);
        } else {
            fieldElement.classList.remove('active');
            console.log(`Désactivé: ${field}`);
        }
    });
}

function saveCriteriaState() {
    const checkboxes = document.querySelectorAll('.criteria-checkbox');
    const state = {};

    checkboxes.forEach(checkbox => {
        const field = checkbox.id.replace('show-', '');
        state[field] = checkbox.checked;
    });

    localStorage.setItem('recherche_criteres_state', JSON.stringify(state));
    console.log('État des critères sauvegardé:', state);
}

// Initialiser les événements des checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.criteria-checkbox');
    const items = document.querySelectorAll('.criteria-checkbox-item');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const field = this.id.replace('show-', '');
            const item = this.closest('.criteria-checkbox-item');

            updateCriteriaField(field, this.checked);

            if (this.checked) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }

            // Sauvegarder automatiquement l'état
            saveCriteriaState();
        });
    });

    // Événement de clic sur les items
    items.forEach(item => {
        item.addEventListener('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = this.querySelector('.criteria-checkbox');
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
                // La sauvegarde se fait automatiquement via l'événement change
            }
        });
    });

    // Restaurer l'état sauvegardé des champs
    const savedState = localStorage.getItem('recherche_criteres_state');
    let criteresState = {};

    if (savedState) {
        try {
            criteresState = JSON.parse(savedState);
            console.log('État des critères restauré:', criteresState);
        } catch (e) {
            console.log('Erreur lors de la restauration, utilisation des valeurs par défaut');
            criteresState = {};
        }
    }

    // Initialiser l'état des champs selon la sauvegarde
    checkboxes.forEach(checkbox => {
        const field = checkbox.id.replace('show-', '');

        // Restaurer l'état sauvegardé ou désactiver par défaut
        const isChecked = criteresState[field] || false;
        checkbox.checked = isChecked;
        updateCriteriaField(field, isChecked);

        const item = checkbox.closest('.criteria-checkbox-item');
        if (isChecked) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });

    console.log('Initialisation: état des critères restauré');
});


</script>
{% endblock %}

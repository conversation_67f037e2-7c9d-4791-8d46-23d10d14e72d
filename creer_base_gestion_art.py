#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer la base de données gestion-art et ses tables principales
"""

import mysql.connector
from mysql.connector import Error
import sys

def creer_base_donnees():
    """Créer la base de données gestion-art"""
    print("🗄️ Création de la base de données gestion-art...")
    
    try:
        # Connexion à MySQL sans spécifier de base de données
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Créer la base de données si elle n'existe pas
            cursor.execute("CREATE DATABASE IF NOT EXISTS `gestion-art` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("   ✅ Base de données 'gestion-art' créée/vérifiée")
            
            # Utiliser la base de données
            cursor.execute("USE `gestion-art`")
            
            return True
            
    except Error as e:
        print(f"   ❌ Erreur lors de la création: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def creer_tables_principales():
    """Créer les tables principales avec Flask"""
    print("\n🏗️ Création des tables avec Flask...")
    
    try:
        from art import app
        from db import db
        
        with app.app_context():
            # Créer toutes les tables définies dans les modèles
            db.create_all()
            print("   ✅ Tables créées avec succès")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur lors de la création des tables: {e}")
        return False

def verifier_installation():
    """Vérifier que l'installation est correcte"""
    print("\n🔍 Vérification de l'installation...")
    
    try:
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="gestion-art"
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Lister les tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"   📊 {len(tables)} tables créées:")
            for table in tables:
                print(f"      - {table[0]}")
            
            return True
            
    except Error as e:
        print(f"   ❌ Erreur de vérification: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def initialiser_donnees_base():
    """Initialiser quelques données de base"""
    print("\n📊 Initialisation des données de base...")
    
    try:
        from art import app
        from db import db, VehiculeGAR
        
        with app.app_context():
            # Vérifier si des données existent déjà
            if VehiculeGAR.query.count() == 0:
                print("   💡 Aucune donnée trouvée, vous pouvez:")
                print("      1. Importer vos données existantes")
                print("      2. Utiliser les scripts de création de données de test")
                print("      3. Ajouter des données via l'interface web")
            else:
                count = VehiculeGAR.query.count()
                print(f"   ✅ {count} véhicules GAR déjà présents")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur lors de l'initialisation: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CONFIGURATION DE LA BASE DE DONNÉES GESTION-ART")
    print("=" * 60)
    
    # Étape 1: Créer la base de données
    if not creer_base_donnees():
        print("\n❌ Échec de la création de la base de données")
        sys.exit(1)
    
    # Étape 2: Créer les tables
    if not creer_tables_principales():
        print("\n❌ Échec de la création des tables")
        sys.exit(1)
    
    # Étape 3: Vérifier l'installation
    if not verifier_installation():
        print("\n❌ Échec de la vérification")
        sys.exit(1)
    
    # Étape 4: Initialiser les données
    initialiser_donnees_base()
    
    print("\n" + "=" * 60)
    print("🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS!")
    print("\n📋 Prochaines étapes:")
    print("   1. Tester la connexion: python test_connexion_gestion_art.py")
    print("   2. Lancer l'application: python art.py")
    print("   3. Accéder à l'interface: http://localhost:5000")
    print("\n💡 Notes importantes:")
    print("   - La base de données 'gestion-art' est maintenant configurée")
    print("   - Toutes les tables ont été créées")
    print("   - L'application est prête à être utilisée")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer les 36 nouvelles tables selon architecture_rh_complete.md
"""

import mysql.connector
from mysql.connector import Error
import sys

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

# Script SQL complet selon architecture_rh_complete.md - ORDRE CORRIGÉ
SQL_CREATION_TABLES = [
    # =====================================================
    # ÉTAPE 1: TABLES DE RÉFÉRENCE SANS DÉPENDANCES
    # =====================================================
    """CREATE TABLE referentiel_type_sanction (
      id_type_sanction INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_type_mvt (
      id_type_mvt INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_origine (
      id_origine INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_lieu_medical (
      id_lieu_medical INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL
    )""",

    """CREATE TABLE referentiel_etat_medical (
      id_etat_medical INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(20) NOT NULL
    )""",

    """CREATE TABLE referentiel_type_medical (
      id_type_medical INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_code_maladie (
      id_code_maladie INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL
    )""",

    """CREATE TABLE referentiel_situation_familiale (
      id_situation_familiale INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(20) NOT NULL
    )""",

    """CREATE TABLE referentiel_type_permission (
      id_type_permission INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_type_absence (
      id_type_absence INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_type_fonction (
      id_type_fonction INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL,
      bulle_condi TEXT
    )""",

    """CREATE TABLE referentiel_type_note (
      id_type_note INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(50) NOT NULL
    )""",

    """CREATE TABLE referentiel_categorie_grade (
      id_categorie_grade INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL
    )""",

    # =====================================================
    # ÉTAPE 2: TABLES DE BASE SANS DÉPENDANCES
    # =====================================================
    """CREATE TABLE Ville (
      id_ville INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL,
      province VARCHAR(100),
      designation_arabe VARCHAR(100)
    )""",

    """CREATE TABLE Personne (
      id_pers INT AUTO_INCREMENT PRIMARY KEY,
      nom VARCHAR(100) NOT NULL,
      prenom VARCHAR(100) NOT NULL,
      nom_arabe VARCHAR(100) NOT NULL,
      prenom_arabe VARCHAR(100) NOT NULL,
      date_naissance DATE NOT NULL,
      date_deces DATE,
      email VARCHAR(150),
      tel VARCHAR(50),
      scan_image LONGBLOB,
      surnom VARCHAR(100),
      num_passport VARCHAR(50),
      num_cine VARCHAR(50),
      scan_cine LONGBLOB,
      scan_passport LONGBLOB,
      scan_acte_nais LONGBLOB,
      gpe_sanguin VARCHAR(10)
    )""",

    """CREATE TABLE Arm (
      id_arm INT AUTO_INCREMENT PRIMARY KEY,
      specialite VARCHAR(100),
      designation VARCHAR(100) NOT NULL
    )""",

    """CREATE TABLE Mission (
      id_mission INT AUTO_INCREMENT PRIMARY KEY,
      date_debut DATE,
      date_fin DATE,
      designation VARCHAR(100),
      reference VARCHAR(100)
    )""",

    # =====================================================
    # ÉTAPE 3: TABLES AVEC DÉPENDANCES SIMPLES
    # =====================================================
    """CREATE TABLE Adresse (
      id_adresse INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(200) NOT NULL,
      designation_arabe VARCHAR(200),
      id_ville INT NOT NULL,
      id_pers INT NOT NULL,
      FOREIGN KEY (id_ville) REFERENCES Ville(id_ville),
      FOREIGN KEY (id_pers) REFERENCES Personne(id_pers) ON DELETE CASCADE
    )""",

    """CREATE TABLE Unite (
      id_organe INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL,
      id_arm INT,
      FOREIGN KEY (id_arm) REFERENCES Arm(id_arm)
    )""",

    """CREATE TABLE Sous_unite (
      id_sous_unite INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL,
      id_organe INT NOT NULL,
      FOREIGN KEY (id_organe) REFERENCES Unite(id_organe)
    )""",

    """CREATE TABLE referentiel_grade (
      id_grade INT AUTO_INCREMENT PRIMARY KEY,
      designation VARCHAR(100) NOT NULL,
      id_categorie_grade INT NOT NULL,
      FOREIGN KEY (id_categorie_grade) REFERENCES referentiel_categorie_grade(id_categorie_grade)
    )""",

    # =====================================================
    # ÉTAPE 4: TABLE MILITAIRE (CENTRALE)
    # =====================================================
    """CREATE TABLE Militaire (
      id_e_mili INT AUTO_INCREMENT PRIMARY KEY,
      matricule VARCHAR(20) NOT NULL UNIQUE,
      num_mutuel VARCHAR(50),
      origine_id INT NOT NULL,
      statut VARCHAR(50),
      date_engagement DATE NOT NULL,
      situation_fam_id INT NOT NULL,
      fonction VARCHAR(100),
      id_pers INT NOT NULL UNIQUE,
      id_grade INT NOT NULL,
      id_organe INT,
      FOREIGN KEY (origine_id) REFERENCES referentiel_origine(id_origine),
      FOREIGN KEY (situation_fam_id) REFERENCES referentiel_situation_familiale(id_situation_familiale),
      FOREIGN KEY (id_pers) REFERENCES Personne(id_pers) ON DELETE CASCADE,
      FOREIGN KEY (id_grade) REFERENCES referentiel_grade(id_grade),
      FOREIGN KEY (id_organe) REFERENCES Unite(id_organe)
    )""",

    # =====================================================
    # ÉTAPE 5: TABLES DÉPENDANTES DE MILITAIRE
    # =====================================================
    """CREATE TABLE Conjointe (
      id_conjointe INT PRIMARY KEY,
      id_e_mili INT NOT NULL,
      scan_acte_mariage LONGBLOB,
      FOREIGN KEY (id_conjointe) REFERENCES Personne(id_pers) ON DELETE CASCADE,
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Enfant (
      id_enfant INT PRIMARY KEY,
      id_e_mili INT NOT NULL,
      sexe VARCHAR(10) NOT NULL,
      FOREIGN KEY (id_enfant) REFERENCES Personne(id_pers) ON DELETE CASCADE,
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Fonction (
      id_fonction INT AUTO_INCREMENT PRIMARY KEY,
      date_debut DATE NOT NULL,
      date_fin DATE,
      reference VARCHAR(100),
      decision VARCHAR(100),
      date_decision DATE,
      id_type_fonction INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_fonction) REFERENCES referentiel_type_fonction(id_type_fonction),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Notation (
      id_note INT AUTO_INCREMENT PRIMARY KEY,
      note VARCHAR(200),
      date DATE,
      tenue VARCHAR(100),
      disponibilite VARCHAR(50),
      moralite VARCHAR(50),
      autorite VARCHAR(50),
      execution VARCHAR(50),
      qualification VARCHAR(50),
      adaptation VARCHAR(50),
      rendement VARCHAR(50),
      sit_medical VARCHAR(50),
      esprit_equipe VARCHAR(50),
      discipline VARCHAR(50),
      condi_physique VARCHAR(50),
      commandement VARCHAR(50),
      scan_note LONGBLOB,
      observation TEXT,
      id_type_note INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_note) REFERENCES referentiel_type_note(id_type_note),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Mouvement (
      id_mvt INT AUTO_INCREMENT PRIMARY KEY,
      date DATE NOT NULL,
      scan_demande LONGBLOB,
      reference VARCHAR(100),
      id_type_mvt INT NOT NULL,
      origine_id INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_mvt) REFERENCES referentiel_type_mvt(id_type_mvt),
      FOREIGN KEY (origine_id) REFERENCES referentiel_origine(id_origine),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Punition (
      id_sanction INT AUTO_INCREMENT PRIMARY KEY,
      date DATE NOT NULL,
      motif VARCHAR(200),
      observation TEXT,
      duree INT,
      reference VARCHAR(100),
      id_type_sanction INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_sanction) REFERENCES referentiel_type_sanction(id_type_sanction),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Medical (
      id_medical INT AUTO_INCREMENT PRIMARY KEY,
      date_debut DATE NOT NULL,
      date_fin DATE,
      duree INT,
      reference VARCHAR(100),
      observation TEXT,
      etat_id INT NOT NULL,
      id_lieu_medical INT NOT NULL,
      id_type_medical INT NOT NULL,
      id_code_maladie INT,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (etat_id) REFERENCES referentiel_etat_medical(id_etat_medical),
      FOREIGN KEY (id_lieu_medical) REFERENCES referentiel_lieu_medical(id_lieu_medical),
      FOREIGN KEY (id_type_medical) REFERENCES referentiel_type_medical(id_type_medical),
      FOREIGN KEY (id_code_maladie) REFERENCES referentiel_code_maladie(id_code_maladie),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Permission (
      id_permission INT AUTO_INCREMENT PRIMARY KEY,
      num_serie BIGINT NOT NULL,
      date_demande DATETIME NOT NULL,
      date_debut DATE NOT NULL,
      date_fin DATE NOT NULL,
      motif VARCHAR(200),
      approbation_chef BOOLEAN NOT NULL,
      adresse VARCHAR(200),
      scan_demande LONGBLOB,
      duree INT,
      id_type_permission INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_permission) REFERENCES referentiel_type_permission(id_type_permission),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Absence (
      id_absence INT AUTO_INCREMENT PRIMARY KEY,
      date_absence DATE NOT NULL,
      date_rejoint DATE,
      motif VARCHAR(200),
      reference VARCHAR(100),
      lieu VARCHAR(200),
      id_type_absence INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_type_absence) REFERENCES referentiel_type_absence(id_type_absence),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Promotion (
      id_promotion INT AUTO_INCREMENT PRIMARY KEY,
      date DATE NOT NULL,
      reference VARCHAR(100),
      type VARCHAR(50),
      observation TEXT,
      id_grade_precedent INT NOT NULL,
      id_grade_suivant INT NOT NULL,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_grade_precedent) REFERENCES referentiel_grade(id_grade),
      FOREIGN KEY (id_grade_suivant) REFERENCES referentiel_grade(id_grade),
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Evenement (
      id_evenement INT AUTO_INCREMENT PRIMARY KEY,
      objet VARCHAR(200),
      reference VARCHAR(100),
      date DATE,
      scan_doc LONGBLOB,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Permanence (
      id_permanence INT AUTO_INCREMENT PRIMARY KEY,
      date DATE NOT NULL,
      tour_aid VARCHAR(50),
      tour VARCHAR(50),
      observation TEXT,
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    """CREATE TABLE Tache (
      id_tache INT AUTO_INCREMENT PRIMARY KEY,
      libelle VARCHAR(200),
      observation TEXT,
      etat VARCHAR(50),
      id_e_mili INT NOT NULL,
      FOREIGN KEY (id_e_mili) REFERENCES Militaire(id_e_mili) ON DELETE CASCADE
    )""",

    # =====================================================
    # ÉTAPE 6: TABLES AVEC DÉPENDANCES COMPLEXES
    # =====================================================
    """CREATE TABLE Jugement (
      id_jugement INT AUTO_INCREMENT PRIMARY KEY,
      nom_tribunal VARCHAR(100),
      num_dossier VARCHAR(50),
      type_jugement VARCHAR(50),
      decision VARCHAR(200),
      reference VARCHAR(100),
      date_jugement DATE,
      id_sanction INT,
      FOREIGN KEY (id_sanction) REFERENCES Punition(id_sanction) ON DELETE CASCADE
    )"""
]

def creer_nouvelles_tables():
    """Créer toutes les nouvelles tables selon l'architecture complète"""
    print("🏗️ CRÉATION DES NOUVELLES TABLES RH")
    print("=" * 60)
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Exécuter les commandes SQL dans l'ordre
        print("   📝 Exécution des commandes SQL dans l'ordre...")

        tables_creees = 0
        for i, commande in enumerate(SQL_CREATION_TABLES):
            try:
                cursor.execute(commande)
                # Extraire le nom de la table
                nom_table = commande.split('(')[0].split()[-1].strip('`')
                print(f"   ✅ Table '{nom_table}' créée ({i+1}/{len(SQL_CREATION_TABLES)})")
                tables_creees += 1
            except Error as e:
                print(f"   ❌ Erreur création table {i+1}: {e}")
                print(f"      Commande: {commande[:100]}...")
                # Continuer avec les autres tables
        
        connection.commit()
        
        print(f"\n📊 {tables_creees} tables créées avec succès")
        return True
        
    except Error as e:
        print(f"\n❌ ERREUR DE CONNEXION: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("   🔌 Connexion fermée")

def verifier_creation():
    """Vérifier que toutes les tables ont été créées"""
    print("\n🔍 VÉRIFICATION DE LA CRÉATION")
    print("-" * 40)
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Lister toutes les tables
        cursor.execute("SHOW TABLES")
        toutes_tables = [table[0] for table in cursor.fetchall()]
        
        # Tables attendues selon l'architecture
        tables_attendues = [
            # Référentiels (13)
            'referentiel_type_sanction', 'referentiel_type_mvt', 'referentiel_origine',
            'referentiel_lieu_medical', 'referentiel_etat_medical', 'referentiel_type_medical',
            'referentiel_code_maladie', 'referentiel_situation_familiale', 'referentiel_type_permission',
            'referentiel_type_absence', 'referentiel_type_fonction', 'referentiel_type_note',
            'referentiel_categorie_grade',
            # Données (23)
            'Ville', 'Personne', 'Adresse', 'Arm', 'Unite', 'Sous_unite', 'referentiel_grade',
            'Militaire', 'Conjointe', 'Enfant', 'Fonction', 'Notation', 'Mission', 'Mouvement',
            'Punition', 'Jugement', 'Medical', 'Permission', 'Absence', 'Promotion', 'Evenement',
            'Permanence', 'Tache'
        ]
        
        tables_rh_presentes = [table for table in toutes_tables if table in tables_attendues]
        tables_manquantes = [table for table in tables_attendues if table not in toutes_tables]
        
        print(f"   ✅ {len(tables_rh_presentes)}/36 tables RH créées")
        
        if tables_manquantes:
            print(f"   ⚠️ {len(tables_manquantes)} tables manquantes:")
            for table in tables_manquantes:
                print(f"      - {table}")
            return False
        else:
            print("   🎉 Toutes les 36 tables ont été créées!")
            return True
            
    except Error as e:
        print(f"   ❌ Erreur de vérification: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 MIGRATION ARCHITECTURE RH COMPLÈTE - ÉTAPE 2")
    print("Création des nouvelles tables (36 tables)")
    print("=" * 60)
    
    # Créer les tables
    if creer_nouvelles_tables():
        # Vérifier la création
        if verifier_creation():
            print("\n🎉 ÉTAPE 2 TERMINÉE AVEC SUCCÈS!")
            print("💡 Vous pouvez maintenant exécuter l'étape 3: population des référentiels")
        else:
            print("\n⚠️ Création incomplète - vérifiez manuellement")
    else:
        print("\n❌ ÉCHEC DE LA CRÉATION")
        sys.exit(1)

if __name__ == "__main__":
    main()

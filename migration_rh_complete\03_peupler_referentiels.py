#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour peupler les 13 tables de référence avec les valeurs spécifiées dans architecture_rh_complete.md
"""

import mysql.connector
from mysql.connector import Error
import sys

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

# Données de référence selon architecture_rh_complete.md
DONNEES_REFERENTIELS = {
    'referentiel_type_sanction': [
        'blâme',
        'avertissement', 
        'arrêt simple',
        'arrêt de rigueur',
        'jugement'
    ],
    
    'referentiel_type_mvt': [
        'détachement',
        'mutation',
        'engagement'
    ],
    
    'referentiel_origine': [
        'ARM',
        'ERART', 
        'HDT'
    ],
    
    'referentiel_lieu_medical': [
        'Hôpital Militaire Mohammed V',
        'Hôpital Militaire Avicenne',
        'Infirmerie de Garnison',
        'Centre Médical Régional',
        'Hôpital Civil'
    ],
    
    'referentiel_etat_medical': [
        'Présent',
        'Hors arme',
        'Absent'
    ],
    
    'referentiel_type_medical': [
        'consultation',
        'hospitalisation',
        'vaccin',
        'test psychotechnique'
    ],
    
    'referentiel_code_maladie': [
        'Grippe saisonnière',
        'Hypertension artérielle',
        'Diabète type 2',
        'Troubles musculo-squelettiques',
        'Stress post-traumatique',
        'Allergie alimentaire',
        'Asthme',
        'Dépression',
        'Anxiété',
        'Troubles du sommeil'
    ],
    
    'referentiel_situation_familiale': [
        'célibataire',
        'marié',
        'veuve'
    ],
    
    'referentiel_type_permission': [
        'normale nationale',
        'normale étranger',
        'exceptionnelle',
        'congé maternité',
        'PTC'
    ],
    
    'referentiel_type_absence': [
        'Désertion',
        'libération',
        'disparition',
        'Décès'
    ],
    
    'referentiel_type_fonction': [
        ('Commandant d\'Unité', 'Responsabilité de commandement d\'une unité opérationnelle'),
        ('Chef de Section', 'Encadrement direct d\'une section'),
        ('Instructeur', 'Formation et instruction du personnel'),
        ('Secrétaire', 'Gestion administrative'),
        ('Mécanicien', 'Maintenance des équipements'),
        ('Chauffeur', 'Conduite des véhicules militaires'),
        ('Cuisinier', 'Préparation des repas'),
        ('Infirmier', 'Soins médicaux de base'),
        ('Transmetteur', 'Communications radio'),
        ('Sentinelle', 'Surveillance et sécurité')
    ],
    
    'referentiel_type_note': [
        'Annuelle',
        'Mutation',
        'Fonction'
    ],
    
    'referentiel_categorie_grade': [
        'Officier Général',
        'Officier Supérieur',
        'Officier Subalternes',
        'ODR',
        'MDR'
    ]
}

# Grades militaires par catégorie
GRADES_MILITAIRES = {
    'Officier Général': ['GEN', 'GBR'],
    'Officier Supérieur': ['COL', 'LCL', 'CDT'],
    'Officier Subalternes': ['CPT', 'LTN', 'SLT'],
    'ODR': ['ADJ', 'SGC', 'SGT'],
    'MDR': ['CPL', 'SOL1', 'SOL2']
}

# Armes et spécialités
ARMES_SPECIALITES = [
    ('Artillerie', 'Sol-Sol'),
    ('Artillerie', 'Sol-Air'),
    ('Blindé', 'Char de combat'),
    ('Blindé', 'Véhicule blindé'),
    ('Infanterie', 'Fusilier'),
    ('Infanterie', 'Parachutiste'),
    ('Transmission', 'Radio'),
    ('Transmission', 'Informatique'),
    ('Génie', 'Combat'),
    ('Génie', 'Construction')
]

# Unités militaires
UNITES_MILITAIRES = [
    ('1GAR', 'Artillerie'),
    ('2GAR', 'Artillerie'),
    ('3GAR', 'Artillerie'),
    ('4GAR', 'Artillerie'),
    ('5GAR', 'Artillerie'),
    ('État-Major', None),
    ('Centre d\'Instruction', None),
    ('Service de Santé', None),
    ('Service des Transmissions', None),
    ('Service du Génie', None)
]

def peupler_referentiel_simple(table, valeurs):
    """Peupler une table de référentiel simple (id, designation)"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for valeur in valeurs:
            cursor.execute(f"INSERT INTO {table} (designation) VALUES (%s)", (valeur,))
            count += 1
        
        connection.commit()
        print(f"   ✅ {table}: {count} valeurs insérées")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur {table}: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def peupler_referentiel_fonction():
    """Peupler le référentiel des fonctions (avec bulle_condi)"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for designation, bulle_condi in DONNEES_REFERENTIELS['referentiel_type_fonction']:
            cursor.execute(
                "INSERT INTO referentiel_type_fonction (designation, bulle_condi) VALUES (%s, %s)",
                (designation, bulle_condi)
            )
            count += 1
        
        connection.commit()
        print(f"   ✅ referentiel_type_fonction: {count} valeurs insérées")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur referentiel_type_fonction: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def peupler_grades():
    """Peupler les grades avec leurs catégories"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for categorie, grades in GRADES_MILITAIRES.items():
            # Récupérer l'ID de la catégorie
            cursor.execute("SELECT id_categorie_grade FROM referentiel_categorie_grade WHERE designation = %s", (categorie,))
            result = cursor.fetchone()
            if result:
                id_categorie = result[0]
                
                for grade in grades:
                    cursor.execute(
                        "INSERT INTO referentiel_grade (designation, id_categorie_grade) VALUES (%s, %s)",
                        (grade, id_categorie)
                    )
                    count += 1
        
        connection.commit()
        print(f"   ✅ referentiel_grade: {count} grades insérés")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur referentiel_grade: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def peupler_armes():
    """Peupler les armes et spécialités"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for arme, specialite in ARMES_SPECIALITES:
            cursor.execute(
                "INSERT INTO Arm (designation, specialite) VALUES (%s, %s)",
                (arme, specialite)
            )
            count += 1
        
        connection.commit()
        print(f"   ✅ Arm: {count} armes/spécialités insérées")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur Arm: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def peupler_unites():
    """Peupler les unités militaires"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for unite, arme in UNITES_MILITAIRES:
            id_arm = None
            if arme:
                cursor.execute("SELECT id_arm FROM Arm WHERE designation = %s LIMIT 1", (arme,))
                result = cursor.fetchone()
                if result:
                    id_arm = result[0]
            
            cursor.execute(
                "INSERT INTO Unite (designation, id_arm) VALUES (%s, %s)",
                (unite, id_arm)
            )
            count += 1
        
        connection.commit()
        print(f"   ✅ Unite: {count} unités insérées")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur Unite: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def peupler_villes():
    """Peupler quelques villes de base"""
    villes = [
        ('Rabat', 'Rabat-Salé-Kénitra', 'الرباط'),
        ('Casablanca', 'Casablanca-Settat', 'الدار البيضاء'),
        ('Fès', 'Fès-Meknès', 'فاس'),
        ('Marrakech', 'Marrakech-Safi', 'مراكش'),
        ('Agadir', 'Souss-Massa', 'أكادير'),
        ('Tanger', 'Tanger-Tétouan-Al Hoceïma', 'طنجة'),
        ('Meknès', 'Fès-Meknès', 'مكناس'),
        ('Oujda', 'Oriental', 'وجدة')
    ]
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        count = 0
        for ville, province, ville_arabe in villes:
            cursor.execute(
                "INSERT INTO Ville (designation, province, designation_arabe) VALUES (%s, %s, %s)",
                (ville, province, ville_arabe)
            )
            count += 1
        
        connection.commit()
        print(f"   ✅ Ville: {count} villes insérées")
        return True
        
    except Error as e:
        print(f"   ❌ Erreur Ville: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 MIGRATION ARCHITECTURE RH COMPLÈTE - ÉTAPE 3")
    print("Population des référentiels (13 tables + données de base)")
    print("=" * 60)
    
    succes = 0
    total = 0
    
    # Peupler les référentiels simples
    for table, valeurs in DONNEES_REFERENTIELS.items():
        if table != 'referentiel_type_fonction':  # Traité séparément
            total += 1
            if peupler_referentiel_simple(table, valeurs):
                succes += 1
    
    # Peupler le référentiel des fonctions
    total += 1
    if peupler_referentiel_fonction():
        succes += 1
    
    # Peupler les grades (dépend des catégories)
    total += 1
    if peupler_grades():
        succes += 1
    
    # Peupler les armes
    total += 1
    if peupler_armes():
        succes += 1
    
    # Peupler les unités (dépend des armes)
    total += 1
    if peupler_unites():
        succes += 1
    
    # Peupler les villes
    total += 1
    if peupler_villes():
        succes += 1
    
    print("\n" + "=" * 60)
    print(f"📊 RÉSUMÉ: {succes}/{total} référentiels peuplés")
    
    if succes == total:
        print("\n🎉 ÉTAPE 3 TERMINÉE AVEC SUCCÈS!")
        print("💡 Vous pouvez maintenant adapter les modèles SQLAlchemy")
    else:
        print(f"\n⚠️ {total - succes} erreurs détectées")

if __name__ == "__main__":
    main()

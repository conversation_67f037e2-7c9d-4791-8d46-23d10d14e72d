#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour adapter les routes RH à la nouvelle architecture complète
"""

import os
import shutil
from datetime import datetime

def sauvegarder_anciens_fichiers():
    """Sauvegarder les anciens fichiers RH"""
    print("💾 SAUVEGARDE DES ANCIENS FICHIERS RH")
    print("-" * 50)
    
    fichiers_rh = [
        'rh_blueprint.py',
        'rh_routes_personnel.py', 
        'rh_routes_famille.py',
        'rh_routes_medical.py',
        'rh_routes_absences.py',
        'rh_routes_mouvements.py'
    ]
    
    # Créer un dossier de sauvegarde
    backup_dir = f"backup_rh_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    for fichier in fichiers_rh:
        if os.path.exists(fichier):
            shutil.copy2(fichier, os.path.join(backup_dir, fichier))
            print(f"   ✅ {fichier} sauvegardé")
        else:
            print(f"   ⚠️ {fichier} non trouvé")
    
    print(f"   📁 Sauvegarde dans: {backup_dir}")
    return backup_dir

def creer_nouveau_blueprint():
    """Créer le nouveau blueprint RH adapté à l'architecture complète"""
    print("\n🔧 CRÉATION DU NOUVEAU BLUEPRINT RH")
    print("-" * 50)
    
    contenu_blueprint = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blueprint RH pour l'architecture complète (36 tables)
Routes principales pour la gestion des ressources humaines
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
from sqlalchemy import or_, and_, func, desc, text
from rh_models import *
from db import db
import json
import base64

# Création du blueprint RH
rh_bp = Blueprint('rh', __name__, url_prefix='/rh')

print("✅ Nouveau blueprint RH pour architecture complète initialisé")

# ============================================================================
# ROUTES PRINCIPALES
# ============================================================================

@rh_bp.route('/')
def dashboard():
    """Dashboard principal RH avec statistiques"""
    try:
        # Statistiques générales avec SQL direct pour éviter les problèmes de modèles
        stats = {}
        
        # Compter les personnes
        result = db.session.execute(text("SELECT COUNT(*) FROM personne"))
        stats['total_personnes'] = result.scalar() or 0
        
        # Compter les militaires
        result = db.session.execute(text("SELECT COUNT(*) FROM militaire"))
        stats['total_militaires'] = result.scalar() or 0
        
        # Compter par catégorie de grade
        result = db.session.execute(text("""
            SELECT c.designation, COUNT(m.id_e_mili) as count
            FROM referentiel_categorie_grade c
            LEFT JOIN referentiel_grade g ON c.id_categorie_grade = g.id_categorie_grade
            LEFT JOIN militaire m ON g.id_grade = m.id_grade
            GROUP BY c.id_categorie_grade, c.designation
            ORDER BY count DESC
        """))
        stats['par_categorie'] = [{'categorie': row[0], 'count': row[1]} for row in result.fetchall()]
        
        # Compter par unité
        result = db.session.execute(text("""
            SELECT u.designation, COUNT(m.id_e_mili) as count
            FROM unite u
            LEFT JOIN militaire m ON u.id_organe = m.id_organe
            GROUP BY u.id_organe, u.designation
            ORDER BY count DESC
            LIMIT 10
        """))
        stats['par_unite'] = [{'unite': row[0], 'count': row[1]} for row in result.fetchall()]
        
        # Compter par origine
        result = db.session.execute(text("""
            SELECT o.designation, COUNT(m.id_e_mili) as count
            FROM referentiel_origine o
            LEFT JOIN militaire m ON o.id_origine = m.origine_id
            GROUP BY o.id_origine, o.designation
            ORDER BY count DESC
        """))
        stats['par_origine'] = [{'origine': row[0], 'count': row[1]} for row in result.fetchall()]
        
        return render_template('RH/dashboard.html', stats=stats)
        
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('RH/dashboard.html', stats={})

@rh_bp.route('/recherche')
def recherche_personnel():
    """Interface de recherche de personnel"""
    try:
        # Charger les référentiels pour les filtres
        referentiels = {}
        
        # Catégories de grades
        result = db.session.execute(text("SELECT id_categorie_grade, designation FROM referentiel_categorie_grade ORDER BY designation"))
        referentiels['categories'] = [{'id': row[0], 'designation': row[1]} for row in result.fetchall()]
        
        # Grades
        result = db.session.execute(text("""
            SELECT g.id_grade, g.designation, c.designation as categorie
            FROM referentiel_grade g
            JOIN referentiel_categorie_grade c ON g.id_categorie_grade = c.id_categorie_grade
            ORDER BY c.designation, g.designation
        """))
        referentiels['grades'] = [{'id': row[0], 'designation': row[1], 'categorie': row[2]} for row in result.fetchall()]
        
        # Armes
        result = db.session.execute(text("SELECT id_arm, designation FROM arm ORDER BY designation"))
        referentiels['armes'] = [{'id': row[0], 'designation': row[1]} for row in result.fetchall()]
        
        # Unités
        result = db.session.execute(text("SELECT id_organe, designation FROM unite ORDER BY designation"))
        referentiels['unites'] = [{'id': row[0], 'designation': row[1]} for row in result.fetchall()]
        
        # Origines
        result = db.session.execute(text("SELECT id_origine, designation FROM referentiel_origine ORDER BY designation"))
        referentiels['origines'] = [{'id': row[0], 'designation': row[1]} for row in result.fetchall()]
        
        return render_template('RH/recherche_personnel.html', referentiels=referentiels)
        
    except Exception as e:
        flash(f'Erreur lors du chargement de la recherche: {str(e)}', 'error')
        return render_template('RH/recherche_personnel.html', referentiels={})

@rh_bp.route('/api/recherche', methods=['POST'])
def api_recherche():
    """API de recherche de personnel avec la nouvelle architecture"""
    try:
        data = request.get_json()
        
        # Construction de la requête SQL avec jointures
        base_query = """
            SELECT 
                p.id_pers,
                p.nom,
                p.prenom,
                p.nom_arabe,
                p.prenom_arabe,
                p.date_naissance,
                p.email,
                p.tel,
                p.num_cine,
                m.id_e_mili,
                m.matricule,
                m.fonction,
                m.statut,
                m.date_engagement,
                g.designation as grade,
                c.designation as categorie,
                u.designation as unite,
                a.designation as arme,
                o.designation as origine,
                sf.designation as situation_familiale
            FROM personne p
            JOIN militaire m ON p.id_pers = m.id_pers
            LEFT JOIN referentiel_grade g ON m.id_grade = g.id_grade
            LEFT JOIN referentiel_categorie_grade c ON g.id_categorie_grade = c.id_categorie_grade
            LEFT JOIN unite u ON m.id_organe = u.id_organe
            LEFT JOIN arm a ON u.id_arm = a.id_arm
            LEFT JOIN referentiel_origine o ON m.origine_id = o.id_origine
            LEFT JOIN referentiel_situation_familiale sf ON m.situation_fam_id = sf.id_situation_familiale
        """
        
        conditions = []
        params = {}
        
        # Critères de recherche
        if data.get('search'):
            conditions.append("(p.nom LIKE :search OR p.prenom LIKE :search OR m.matricule LIKE :search)")
            params['search'] = f"%{data.get('search')}%"
        
        if data.get('matricule'):
            conditions.append("m.matricule LIKE :matricule")
            params['matricule'] = f"%{data.get('matricule')}%"
        
        if data.get('cin'):
            conditions.append("p.num_cine LIKE :cin")
            params['cin'] = f"%{data.get('cin')}%"
        
        if data.get('gsm'):
            conditions.append("p.tel LIKE :gsm")
            params['gsm'] = f"%{data.get('gsm')}%"
        
        if data.get('grade_id'):
            conditions.append("m.id_grade = :grade_id")
            params['grade_id'] = data.get('grade_id')
        
        if data.get('categorie_id'):
            conditions.append("g.id_categorie_grade = :categorie_id")
            params['categorie_id'] = data.get('categorie_id')
        
        if data.get('arme_id'):
            conditions.append("a.id_arm = :arme_id")
            params['arme_id'] = data.get('arme_id')
        
        if data.get('unite_id'):
            conditions.append("u.id_organe = :unite_id")
            params['unite_id'] = data.get('unite_id')
        
        if data.get('origine_id'):
            conditions.append("o.id_origine = :origine_id")
            params['origine_id'] = data.get('origine_id')
        
        # Construire la requête finale
        if conditions:
            query = base_query + " WHERE " + " AND ".join(conditions)
        else:
            query = base_query
        
        query += " ORDER BY p.nom, p.prenom LIMIT 100"
        
        # Exécuter la requête
        result = db.session.execute(text(query), params)
        
        # Formater les résultats
        personnel = []
        for row in result.fetchall():
            personnel.append({
                'id_pers': row[0],
                'nom': row[1],
                'prenom': row[2],
                'nom_arabe': row[3],
                'prenom_arabe': row[4],
                'date_naissance': row[5].strftime('%Y-%m-%d') if row[5] else None,
                'email': row[6],
                'tel': row[7],
                'num_cine': row[8],
                'id_e_mili': row[9],
                'matricule': row[10],
                'fonction': row[11],
                'statut': row[12],
                'date_engagement': row[13].strftime('%Y-%m-%d') if row[13] else None,
                'grade': row[14],
                'categorie': row[15],
                'unite': row[16],
                'arme': row[17],
                'origine': row[18],
                'situation_familiale': row[19],
                'nom_complet': f"{row[2]} {row[1]}",
                'age': calculate_age(row[5]) if row[5] else None
            })
        
        return jsonify({
            'success': True,
            'personnel': personnel,
            'total': len(personnel),
            'criteres': data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def calculate_age(date_naissance):
    """Calculer l'âge à partir de la date de naissance"""
    if not date_naissance:
        return None
    today = date.today()
    return today.year - date_naissance.year - ((today.month, today.day) < (date_naissance.month, date_naissance.day))

@rh_bp.route('/personnel/<int:id_pers>')
def fiche_personnel(id_pers):
    """Afficher la fiche complète d'un personnel"""
    try:
        # Récupérer les informations complètes avec jointures
        result = db.session.execute(text("""
            SELECT 
                p.*,
                m.*,
                g.designation as grade,
                c.designation as categorie,
                u.designation as unite,
                a.designation as arme,
                o.designation as origine,
                sf.designation as situation_familiale,
                v.designation as ville,
                v.province
            FROM personne p
            LEFT JOIN militaire m ON p.id_pers = m.id_pers
            LEFT JOIN referentiel_grade g ON m.id_grade = g.id_grade
            LEFT JOIN referentiel_categorie_grade c ON g.id_categorie_grade = c.id_categorie_grade
            LEFT JOIN unite u ON m.id_organe = u.id_organe
            LEFT JOIN arm a ON u.id_arm = a.id_arm
            LEFT JOIN referentiel_origine o ON m.origine_id = o.id_origine
            LEFT JOIN referentiel_situation_familiale sf ON m.situation_fam_id = sf.id_situation_familiale
            LEFT JOIN adresse ad ON p.id_pers = ad.id_pers
            LEFT JOIN ville v ON ad.id_ville = v.id_ville
            WHERE p.id_pers = :id_pers
        """), {'id_pers': id_pers})
        
        row = result.fetchone()
        if not row:
            flash('Personnel non trouvé', 'error')
            return redirect(url_for('rh.recherche_personnel'))
        
        # Construire l'objet personnel
        personnel = {
            'id_pers': row[0],
            'nom': row[1],
            'prenom': row[2],
            'nom_arabe': row[3],
            'prenom_arabe': row[4],
            'date_naissance': row[5],
            'date_deces': row[6],
            'email': row[7],
            'tel': row[8],
            'surnom': row[10],
            'num_passport': row[11],
            'num_cine': row[12],
            'gpe_sanguin': row[16],
            'age': calculate_age(row[5]) if row[5] else None
        }
        
        # Informations militaires si c'est un militaire
        militaire = None
        if row[17]:  # id_e_mili
            militaire = {
                'id_e_mili': row[17],
                'matricule': row[18],
                'num_mutuel': row[19],
                'statut': row[21],
                'date_engagement': row[22],
                'fonction': row[24],
                'grade': row[27],
                'categorie': row[28],
                'unite': row[29],
                'arme': row[30],
                'origine': row[31],
                'situation_familiale': row[32],
                'ville': row[33],
                'province': row[34]
            }
        
        return render_template('RH/fiche_personnel_complete.html', 
                             personnel=personnel, 
                             militaire=militaire)
        
    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# ============================================================================
# ROUTES POUR GESTION DES DOCUMENTS BLOB
# ============================================================================

@rh_bp.route('/document/<int:id_pers>/<string:type_doc>')
def afficher_document(id_pers, type_doc):
    """Afficher un document scanné (BLOB)"""
    try:
        # Mapping des types de documents
        champs_documents = {
            'photo': 'scan_image',
            'cine': 'scan_cine', 
            'passport': 'scan_passport',
            'acte_naissance': 'scan_acte_nais'
        }
        
        if type_doc not in champs_documents:
            return "Type de document non valide", 400
        
        champ = champs_documents[type_doc]
        
        result = db.session.execute(text(f"SELECT {champ} FROM personne WHERE id_pers = :id_pers"), 
                                  {'id_pers': id_pers})
        row = result.fetchone()
        
        if not row or not row[0]:
            return "Document non trouvé", 404
        
        # Retourner le document avec le bon content-type
        content_types = {
            'photo': 'image/jpeg',
            'cine': 'image/jpeg',
            'passport': 'image/jpeg', 
            'acte_naissance': 'application/pdf'
        }
        
        from flask import Response
        return Response(row[0], mimetype=content_types.get(type_doc, 'application/octet-stream'))
        
    except Exception as e:
        return f"Erreur: {str(e)}", 500

print("✅ Nouveau blueprint RH créé avec architecture complète")
'''
    
    with open('rh_blueprint_nouveau.py', 'w', encoding='utf-8') as f:
        f.write(contenu_blueprint)
    
    print("   ✅ rh_blueprint_nouveau.py créé")
    return True

def main():
    """Fonction principale"""
    print("🚀 ADAPTATION DES ROUTES RH À L'ARCHITECTURE COMPLÈTE")
    print("=" * 70)
    
    # Sauvegarder les anciens fichiers
    backup_dir = sauvegarder_anciens_fichiers()
    
    # Créer le nouveau blueprint
    if creer_nouveau_blueprint():
        print("\n🎉 ADAPTATION TERMINÉE AVEC SUCCÈS!")
        print(f"💾 Anciens fichiers sauvegardés dans: {backup_dir}")
        print("📝 Nouveau blueprint créé: rh_blueprint_nouveau.py")
        print("\n💡 PROCHAINES ÉTAPES:")
        print("   1. Remplacer rh_blueprint.py par rh_blueprint_nouveau.py")
        print("   2. Adapter les templates RH")
        print("   3. Tester les nouvelles routes")
    else:
        print("\n❌ ERREUR LORS DE L'ADAPTATION")

if __name__ == "__main__":
    main()

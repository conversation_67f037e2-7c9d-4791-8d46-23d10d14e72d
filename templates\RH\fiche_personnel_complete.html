{% extends "rh/base_rh.html" %}

{% block title %}{{ militaire.nom_complet }} - Fiche Personnel{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête de la Fiche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-user-shield"></i>
                                {{ militaire.nom_complet }}
                            </h1>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark me-2">{{ militaire.matricule }}</span>
                                <span class="badge bg-info me-2">{{ militaire.unite.libelle if militaire.unite else 'N/A' }}</span>
                                <span class="badge bg-warning text-dark">{{ militaire.arme.libelle if militaire.arme else 'N/A' }}</span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-warning" onclick="window.print()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                                <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Onglets Principaux -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <ul class="nav nav-tabs card-header-tabs" id="fichePersonnelTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personnel-tab" data-bs-toggle="tab" data-bs-target="#personnel" type="button" role="tab">
                                <i class="fas fa-user"></i> Personnel
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="famille-tab" data-bs-toggle="tab" data-bs-target="#famille" type="button" role="tab">
                                <i class="fas fa-home"></i> Famille
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="medical-tab" data-bs-toggle="tab" data-bs-target="#medical" type="button" role="tab">
                                <i class="fas fa-heartbeat"></i> Médical
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absences-tab" data-bs-toggle="tab" data-bs-target="#absences" type="button" role="tab">
                                <i class="fas fa-calendar-times"></i> Absences
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="mouvements-tab" data-bs-toggle="tab" data-bs-target="#mouvements" type="button" role="tab">
                                <i class="fas fa-route"></i> Mouvements
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="historique-tab" data-bs-toggle="tab" data-bs-target="#historique" type="button" role="tab">
                                <i class="fas fa-history"></i> Historique
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="fichePersonnelTabsContent">
                        <!-- Onglet Personnel -->
                        <div class="tab-pane fade show active" id="personnel" role="tabpanel">
                            <div class="row">
                                <!-- Informations Personnelles -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Informations Personnelles</h5>
                                        <a href="{{ url_for('rh.modifier_personnel', matricule=militaire.matricule) }}" class="btn btn-success-military btn-sm">
                                            <i class="fas fa-edit me-1"></i>Modifier
                                        </a>
                                    </div>
                                    <table class="table table-sm">
                                        <tr><td><strong>Nom complet :</strong></td><td>{{ militaire.nom }} {{ militaire.prenom }}</td></tr>
                                        <tr><td><strong>Nom (Arabe) :</strong></td><td dir="rtl">{{ militaire.nom_arabe }} {{ militaire.prenom_arabe }}</td></tr>
                                        <tr><td><strong>Date de naissance :</strong></td><td>{{ militaire.date_naissance.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu de naissance :</strong></td><td>{{ militaire.lieu_naissance }}</td></tr>
                                        <tr><td><strong>Âge :</strong></td><td>{{ age }} ans</td></tr>
                                        <tr><td><strong>Genre :</strong></td><td>{{ militaire.genre.libelle if militaire.genre else 'N/A' }}</td></tr>
                                        <tr><td><strong>Groupe sanguin :</strong></td><td><span class="badge bg-danger">{{ militaire.groupe_sanguin.libelle if militaire.groupe_sanguin else 'N/A' }}</span></td></tr>
                                        <tr><td><strong>Taille :</strong></td><td>{{ militaire.taille }} cm</td></tr>
                                        <tr><td><strong>État matrimonial :</strong></td><td>
                                            {% if militaire.situation_familiale %}
                                                <span class="badge bg-info">{{ militaire.situation_familiale.libelle }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Non renseigné</span>
                                            {% endif %}
                                        </td></tr>
                                        <tr><td><strong>Langues parlées :</strong></td><td>
                                            {% if langues %}
                                                {% for lang in langues %}
                                                    <span class="badge bg-primary me-1">{{ lang.langue.libelle if lang.langue else 'N/A' }}</span>
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">Aucune langue renseignée</span>
                                            {% endif %}
                                        </td></tr>
                                    </table>
                                </div>

                                <!-- Informations Militaires -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-shield-alt me-2"></i>Informations Militaires</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>Matricule :</strong></td><td><span class="badge bg-primary">{{ militaire.matricule }}</span></td></tr>
                                        <tr><td><strong>Grade :</strong></td><td><span class="badge bg-success">{{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }}</span></td></tr>
                                        <tr><td><strong>Catégorie :</strong></td><td>{{ militaire.categorie.libelle if militaire.categorie else 'N/A' }}</td></tr>
                                        <tr><td><strong>Service/Arme :</strong></td><td>{{ militaire.arme.libelle if militaire.arme else 'N/A' }}</td></tr>
                                        <tr><td><strong>Spécialité :</strong></td><td>{{ militaire.specialite.libelle if militaire.specialite else 'Aucune' }}</td></tr>
                                        <tr><td><strong>Unité :</strong></td><td>{{ militaire.unite.libelle if militaire.unite else 'N/A' }}</td></tr>
                                        <tr><td><strong>Fonction :</strong></td><td>{{ militaire.fonction }}</td></tr>
                                        <tr><td><strong>Date engagement :</strong></td><td>{{ militaire.date_engagement.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Date prise fonction :</strong></td><td>{{ militaire.date_prise_fonction.strftime('%d/%m/%Y') }}</td></tr>
                                    </table>
                                </div>

                                <!-- Documents et Coordonnées -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-id-card me-2"></i>Documents d'Identité</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>CIN :</strong></td><td>{{ militaire.numero_cin }}</td></tr>
                                        <tr><td><strong>Date délivrance :</strong></td><td>{{ militaire.date_delivrance_cin.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Date expiration :</strong></td><td>
                                            {% set jours_restants = (militaire.date_expiration_cin - date.today()).days %}
                                            {{ militaire.date_expiration_cin.strftime('%d/%m/%Y') }}
                                            {% if jours_restants <= 0 %}
                                            <span class="badge bg-danger ms-2">Expirée</span>
                                            {% elif jours_restants <= 30 %}
                                            <span class="badge bg-warning text-dark ms-2">{{ jours_restants }}j</span>
                                            {% endif %}
                                        </td></tr>
                                        {% if militaire.numero_passport %}
                                        <tr><td><strong>Passeport :</strong></td><td>{{ militaire.numero_passport }}</td></tr>
                                        {% endif %}
                                    </table>
                                </div>

                                <!-- Coordonnées -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-phone me-2"></i>Coordonnées</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>GSM :</strong></td><td><a href="tel:{{ militaire.gsm }}">{{ militaire.gsm }}</a></td></tr>
                                        {% if militaire.telephone_domicile %}
                                        <tr><td><strong>Téléphone domicile :</strong></td><td><a href="tel:{{ militaire.telephone_domicile }}">{{ militaire.telephone_domicile }}</a></td></tr>
                                        {% endif %}
                                        <tr><td><strong>Résidence :</strong></td><td>{{ militaire.lieu_residence }}</td></tr>
                                        <tr><td><strong>GSM urgence :</strong></td><td><a href="tel:{{ militaire.gsm_urgence }}">{{ militaire.gsm_urgence }}</a></td></tr>
                                        <tr><td><strong>Lien parenté :</strong></td><td>{{ militaire.degre_parente.libelle if militaire.degre_parente else 'N/A' }}</td></tr>
                                    </table>
                                </div>

                                <!-- Informations Bancaires -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-university me-2"></i>Informations Bancaires</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>CCP :</strong></td><td>{{ militaire.ccp if militaire.ccp else 'Non renseigné' }}</td></tr>
                                        {% if militaire.compte_bancaire %}
                                        <tr><td><strong>Compte bancaire :</strong></td><td>{{ militaire.compte_bancaire }}</td></tr>
                                        {% else %}
                                        <tr><td><strong>Compte bancaire :</strong></td><td>Non renseigné</td></tr>
                                        {% endif %}
                                        <tr><td><strong>N° SOMME :</strong></td><td>{{ militaire.numero_somme if militaire.numero_somme else 'Non renseigné' }}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Famille -->
                        <div class="tab-pane fade" id="famille" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-users me-2"></i>Informations Familiales</h5>
                                <a href="{{ url_for('rh.gestion_famille_simple', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                            </div>
                            <div class="row">
                                <!-- Informations Parents -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-users me-2"></i>Parents</h5>
                                    </div>
                                    <table class="table table-sm">
                                        <tr><td><strong>Père :</strong></td><td>{{ militaire.nom_pere }} {{ militaire.prenom_pere }}</td></tr>
                                        <tr><td><strong>Mère :</strong></td><td>{{ militaire.nom_mere }} {{ militaire.prenom_mere }}</td></tr>
                                        <tr><td><strong>Adresse parents :</strong></td><td>{{ militaire.adresse_parents }}</td></tr>
                                        {% if militaire.nombre_enfants %}
                                        <tr><td><strong>Nombre d'enfants :</strong></td><td>{{ militaire.nombre_enfants }}</td></tr>
                                        {% endif %}
                                    </table>
                                </div>

                                <!-- Conjoint -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-user-friends me-2"></i>Conjoint</h5>
                                    </div>
                                    {% if conjoint %}
                                    <table class="table table-sm table-striped">
                                        <tr><td><strong>Nom complet :</strong></td><td>{{ conjoint.nom }} {{ conjoint.prenom }}</td></tr>
                                        {% if conjoint.nom_arabe and conjoint.prenom_arabe %}
                                        <tr><td><strong>Nom arabe :</strong></td><td>{{ conjoint.nom_arabe }} {{ conjoint.prenom_arabe }}</td></tr>
                                        {% endif %}
                                        <tr><td><strong>Date naissance :</strong></td><td>{{ conjoint.date_naissance.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu naissance :</strong></td><td>{{ conjoint.lieu_naissance }}</td></tr>
                                        {% if conjoint.lieu_naissance_arabe %}
                                        <tr><td><strong>Lieu naissance (arabe) :</strong></td><td>{{ conjoint.lieu_naissance_arabe }}</td></tr>
                                        {% endif %}
                                        <tr><td><strong>CIN :</strong></td><td>{{ conjoint.numero_cin }}</td></tr>
                                        {% if conjoint.adresse %}
                                        <tr><td><strong>Adresse :</strong></td><td>{{ conjoint.adresse }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.adresse_arabe %}
                                        <tr><td><strong>Adresse (arabe) :</strong></td><td>{{ conjoint.adresse_arabe }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.gsm %}
                                        <tr><td><strong>GSM :</strong></td><td><a href="tel:{{ conjoint.gsm }}" class="text-decoration-none">{{ conjoint.gsm }}</a></td></tr>
                                        {% endif %}
                                        <tr><td><strong>Date mariage :</strong></td><td>{{ conjoint.date_mariage.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu mariage :</strong></td><td>{{ conjoint.lieu_mariage }}</td></tr>
                                        {% if conjoint.profession %}
                                        <tr><td><strong>Profession :</strong></td><td>{{ conjoint.profession }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.profession_arabe %}
                                        <tr><td><strong>Profession (arabe) :</strong></td><td>{{ conjoint.profession_arabe }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.nom_pere and conjoint.prenom_pere %}
                                        <tr><td><strong>Père :</strong></td><td>{{ conjoint.nom_pere }} {{ conjoint.prenom_pere }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.nom_arabe_pere and conjoint.prenom_arabe_pere %}
                                        <tr><td><strong>Père (arabe) :</strong></td><td>{{ conjoint.nom_arabe_pere }} {{ conjoint.prenom_arabe_pere }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.profession_pere %}
                                        <tr><td><strong>Profession père :</strong></td><td>{{ conjoint.profession_pere }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.nom_mere and conjoint.prenom_mere %}
                                        <tr><td><strong>Mère :</strong></td><td>{{ conjoint.nom_mere }} {{ conjoint.prenom_mere }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.nom_arabe_mere and conjoint.prenom_arabe_mere %}
                                        <tr><td><strong>Mère (arabe) :</strong></td><td>{{ conjoint.nom_arabe_mere }} {{ conjoint.prenom_arabe_mere }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.profession_mere %}
                                        <tr><td><strong>Profession mère :</strong></td><td>{{ conjoint.profession_mere }}</td></tr>
                                        {% endif %}
                                    </table>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune information de conjoint</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Enfants -->
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-child me-2"></i>Enfants ({{ enfants|length }})</h5>
                                        <a href="{{ url_for('rh.gestion_enfants', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                            <i class="fas fa-edit"></i> Modifier
                                        </a>
                                    </div>
                                    {% if enfants %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Nom Complet</th>
                                                    <th>Sexe</th>
                                                    <th>Date Naissance</th>
                                                    <th>Âge</th>
                                                    <th>Lieu Naissance</th>
                                                    <th>Date Décès</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for enfant in enfants %}
                                                <tr>
                                                    <td><strong>{{ enfant.nom }} {{ enfant.prenom }}</strong></td>
                                                    <td><span class="badge bg-{{ 'primary' if enfant.genre.libelle == 'Masculin' else 'pink' }}">{{ enfant.genre.libelle if enfant.genre else 'N/A' }}</span></td>
                                                    <td>{{ enfant.date_naissance.strftime('%d/%m/%Y') }}</td>
                                                    <td>
                                                        {% if enfant.date_deces %}
                                                            {% set age = ((enfant.date_deces - enfant.date_naissance).days // 365) %}
                                                            {{ age }} ans (décédé)
                                                        {% else %}
                                                            {% set age = ((date.today() - enfant.date_naissance).days // 365) %}
                                                            {{ age }} ans
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ enfant.lieu_naissance }}</td>
                                                    <td>
                                                        {% if enfant.date_deces %}
                                                            {{ enfant.date_deces.strftime('%d/%m/%Y') }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-child fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun enfant enregistré</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Médical -->
                        <div class="tab-pane fade" id="medical" role="tabpanel">
                            <div class="row">
                                <!-- Situation Médicale -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-heartbeat me-2"></i>Situation Médicale</h5>
                                        <a href="{{ url_for('rh.gestion_medical', matricule=militaire.matricule) }}" class="btn btn-sm btn-success-military">
                                            <i class="fas fa-cog"></i> Gérer
                                        </a>
                                    </div>
                                    {% if situation_medicale %}
                                    <table class="table table-sm">
                                        <tr><td><strong>Aptitude :</strong></td><td>
                                            <span class="badge bg-{{ 'success' if situation_medicale.aptitude == 'Apte' else 'danger' }}">
                                                {{ situation_medicale.aptitude }}
                                            </span>
                                        </td></tr>
                                        {% if situation_medicale.date_hospitalisation %}
                                        <tr><td><strong>Dernière hospitalisation :</strong></td><td>{{ situation_medicale.date_hospitalisation.strftime('%d/%m/%Y') }}</td></tr>
                                        {% endif %}
                                        {% if situation_medicale.observations %}
                                        <tr><td><strong>Observations :</strong></td><td>{{ situation_medicale.observations[:100] }}{% if situation_medicale.observations|length > 100 %}...{% endif %}</td></tr>
                                        {% endif %}
                                    </table>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune situation médicale</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Hospitalisations Récentes -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-hospital me-2"></i>Hospitalisations Récentes</h5>
                                    {% if hospitalisations %}
                                    <div class="list-group">
                                        <div class="list-group-item text-center text-muted">
                                            <i class="fas fa-info-circle"></i> Aucune hospitalisation enregistrée
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune hospitalisation</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Vaccinations et PTC -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-syringe me-2"></i>Vaccinations Récentes</h5>
                                    {% if vaccinations %}
                                    <div class="list-group">
                                        {% for vacc in vaccinations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ vacc.objet }}</h6>
                                                <small>{{ vacc.date_vaccination.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            {% if vacc.rappel_prevu %}
                                            <small>Rappel prévu: {{ vacc.rappel_prevu.strftime('%d/%m/%Y') }}</small>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-syringe fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune vaccination</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-bed me-2"></i>PTC Récents</h5>
                                    {% if ptcs %}
                                    <div class="list-group">
                                        {% for ptc in ptcs %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ ptc.objet[:50] }}{% if ptc.objet|length > 50 %}...{% endif %}</h6>
                                                <small>{{ ptc.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>Durée: {{ ptc.duree }} jour(s)</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun PTC</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Absences -->
                        <div class="tab-pane fade" id="absences" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-calendar-times me-2"></i>Gestion des Absences</h5>
                                <a href="{{ url_for('rh.gestion_absences', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-cog"></i> Gérer les Absences
                                </a>
                            </div>

                            <div class="row">
                                <!-- Permissions -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-calendar-check me-2"></i>Permissions ({{ permissions|length }})</h6>
                                    {% if permissions %}
                                    <div class="list-group">
                                        {% for perm in permissions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">Permission</h6>
                                                <small>{{ perm.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>Du {{ perm.date_debut.strftime('%d/%m/%Y') }} au {{ perm.date_fin.strftime('%d/%m/%Y') }}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune permission</p>
                                    {% endif %}
                                </div>

                                <!-- Détachements -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-plane me-2"></i>Détachements ({{ detachements|length }})</h6>
                                    {% if detachements %}
                                    <div class="list-group">
                                        {% for det in detachements %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ det.adresse_detachement }}</h6>
                                                <small>{{ det.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ det.pays }} - Du {{ det.date_debut.strftime('%d/%m/%Y') }} au {{ det.date_fin.strftime('%d/%m/%Y') }}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucun détachement</p>
                                    {% endif %}
                                </div>

                                <!-- Désertions -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Désertions ({{ desertions|length }})</h6>
                                    {% if desertions %}
                                    <div class="list-group">
                                        {% for des in desertions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">Désertion</h6>
                                                <small>{{ des.date_absence.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small class="text-success">
                                                Retour le {{ des.date_retour.strftime('%d/%m/%Y') }}
                                            </small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune désertion</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Mouvements -->
                        <div class="tab-pane fade" id="mouvements" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-route me-2"></i>Mouvements et Sanctions</h5>
                                <a href="{{ url_for('rh.gestion_mouvements', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-cog"></i> Gérer les Mouvements
                                </a>
                            </div>

                            <div class="row">
                                <!-- Mutations -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-exchange-alt me-2"></i>Mutations Inter-Bie ({{ mutations|length }})</h6>
                                    {% if mutations %}
                                    <div class="list-group">
                                        {% for mut in mutations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ mut.service.libelle if mut.service else 'N/A' }}</h6>
                                                <small>{{ mut.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ mut.fonction }}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune mutation</p>
                                    {% endif %}
                                </div>

                                <!-- Séjours Opérationnels -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-map-marked-alt me-2"></i>Séjours Opérationnels ({{ sejours_ops|length }})</h6>
                                    {% if sejours_ops %}
                                    <div class="list-group">
                                        {% for sej in sejours_ops %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ sej.unite.libelle if sej.unite else 'N/A' }}</h6>
                                                <small>{{ sej.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ sej.lieu_operation }} - {% if sej.en_cours %}En cours{% else %}Terminé{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucun séjour opérationnel</p>
                                    {% endif %}
                                </div>

                                <!-- Sanctions -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-gavel me-2"></i>Sanctions ({{ sanctions|length }})</h6>
                                    {% if sanctions %}
                                    <div class="list-group">
                                        {% for sanc in sanctions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ sanc.type_sanction }}</h6>
                                                <small>{{ sanc.date_sanction.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ sanc.motif[:80] }}{% if sanc.motif|length > 80 %}...{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune sanction</p>
                                    {% endif %}
                                </div>

                                <!-- Libérations -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-door-open me-2"></i>Libérations ({{ liberations|length }})</h6>
                                    {% if liberations %}
                                    <div class="list-group">
                                        {% for lib in liberations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ lib.motif }}</h6>
                                                <small>{{ lib.date_liberation.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune libération</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Historique -->
                        <div class="tab-pane fade" id="historique" role="tabpanel">
                            <div class="row">
                                <!-- Évolution des Grades -->
                                <div class="col-lg-4 mb-4">
                                    <div class="card-military">
                                        <div class="card-header-military">
                                            <h5 class="mb-0"><i class="fas fa-star me-2"></i>Évolution des Grades</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if historique_grades %}
                                            <div class="timeline-vertical">
                                                {% for grade_hist in historique_grades %}
                                                <div class="timeline-item-vertical">
                                                    <div class="timeline-marker-vertical bg-success"></div>
                                                    <div class="timeline-content-vertical">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="badge bg-success">{{ grade_hist.grade.libelle }}</span>
                                                            <small class="text-muted">{{ grade_hist.date_nomination.strftime('%d/%m/%Y') }}</small>
                                                        </div>
                                                        <small class="text-muted">{{ grade_hist.grade.categorie.libelle }}</small>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <div class="text-center py-3">
                                                <i class="fas fa-star fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">Aucun historique</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Unités Successives -->
                                <div class="col-lg-4 mb-4">
                                    <div class="card-military">
                                        <div class="card-header-military">
                                            <h5 class="mb-0"><i class="fas fa-building me-2"></i>Unités Successives</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if historique_unites %}
                                            <div class="timeline-vertical">
                                                {% for unite_hist in historique_unites %}
                                                <div class="timeline-item-vertical">
                                                    <div class="timeline-marker-vertical bg-primary"></div>
                                                    <div class="timeline-content-vertical">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="badge bg-primary">{{ unite_hist.unite.libelle }}</span>
                                                            <small class="text-muted">{{ unite_hist.date_affectation.strftime('%d/%m/%Y') }}</small>
                                                        </div>
                                                        <small class="text-muted">{{ unite_hist.unite.type_unite }}</small>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <div class="text-center py-3">
                                                <i class="fas fa-building fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">Aucun historique</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Fonctions Successives -->
                                <div class="col-lg-4 mb-4">
                                    <div class="card-military">
                                        <div class="card-header-military">
                                            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Fonctions Successives</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if historique_fonctions %}
                                            <div class="timeline-vertical">
                                                {% for fonction_hist in historique_fonctions %}
                                                <div class="timeline-item-vertical">
                                                    <div class="timeline-marker-vertical bg-warning"></div>
                                                    <div class="timeline-content-vertical">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="badge bg-warning">{{ fonction_hist.fonction.libelle }}</span>
                                                            <small class="text-muted">{{ fonction_hist.date_prise_fonction.strftime('%d/%m/%Y') }}</small>
                                                        </div>
                                                        {% if fonction_hist.date_fin_fonction %}
                                                        <small class="text-muted">Fin: {{ fonction_hist.date_fin_fonction.strftime('%d/%m/%Y') }}</small>
                                                        {% else %}
                                                        <small class="text-success">En cours</small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <div class="text-center py-3">
                                                <i class="fas fa-briefcase fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">Aucun historique</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Modification Informations Personnelles -->
<div class="modal fade" id="modalModifierPersonnel" tabindex="-1" aria-labelledby="modalModifierPersonnelLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-military">
                <h5 class="modal-title text-white" id="modalModifierPersonnelLabel">
                    <i class="fas fa-edit me-2"></i>Modification Informations Personnelles - {{ militaire.nom_complet }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="{{ url_for('rh.modifier_informations_personnelles', matricule=militaire.matricule) }}" id="formModifierPersonnel">
                    <div class="row">
                        <!-- Section État Matrimonial -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-ring me-1"></i>État Matrimonial</h6>
                                </div>
                                <div class="card-body">
                                    <select class="form-select" name="situation_familiale_id" required>
                                        <option value="">-- Sélectionner --</option>
                                        {% for etat in etats_matrimoniaux %}
                                        <option value="{{ etat.id }}" {% if militaire.situation_familiale_id == etat.id %}selected{% endif %}>
                                            {{ etat.libelle }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Section Grade -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-star me-1"></i>Grade</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Nouveau Grade</label>
                                        <select class="form-select" name="grade_id" id="gradeSelect" required>
                                            <option value="">-- Sélectionner --</option>
                                            {% for grade in grades %}
                                            <option value="{{ grade.id }}"
                                                data-categorie="{{ grade.categorie_id }}"
                                                {% if grade.ordre <= militaire.grade_actuel.ordre %}disabled{% endif %}
                                                {% if grade.ordre == (militaire.grade_actuel.ordre + 1) %}class="text-success fw-bold"{% endif %}>
                                                {{ grade.libelle }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Date de Nomination</label>
                                        <input type="date" class="form-control" name="date_nomination" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Catégorie (automatique)</label>
                                        <input type="text" class="form-control" id="categorieAuto" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Section Unité -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-building me-1"></i>Unité</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Nouvelle Unité</label>
                                        <select class="form-select" name="unite_id" required>
                                            <option value="">-- Sélectionner --</option>
                                            {% for unite in unites %}
                                            <option value="{{ unite.id }}" {% if militaire.unite_id == unite.id %}selected{% endif %}>
                                                {{ unite.libelle }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Date d'Affectation</label>
                                        <input type="date" class="form-control" name="date_affectation" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Fonction -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-1"></i>Fonction</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Nouvelle Fonction</label>
                                        <select class="form-select" name="fonction_id" required>
                                            <option value="">-- Sélectionner --</option>
                                            {% for fonction in fonctions %}
                                            <option value="{{ fonction.id }}" {% if militaire.fonction_id == fonction.id %}selected{% endif %}>
                                                {{ fonction.libelle }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Date Prise de Fonction</label>
                                        <input type="date" class="form-control" name="date_prise_fonction" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Section Contact -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-phone me-1"></i>Contact</h6>
                                </div>
                                <div class="card-body">
                                    <label class="form-label fw-bold">GSM</label>
                                    <input type="tel" class="form-control" name="gsm" value="{{ militaire.gsm or '' }}" placeholder="Numéro de téléphone">
                                </div>
                            </div>
                        </div>

                        <!-- Section Adresse -->
                        <div class="col-md-6 mb-4">
                            <div class="card-military">
                                <div class="card-header-military">
                                    <h6 class="mb-0"><i class="fas fa-home me-1"></i>Adresse</h6>
                                </div>
                                <div class="card-body">
                                    <textarea class="form-control" name="adresse" rows="3" placeholder="Adresse de résidence">{{ militaire.residence or '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="submit" class="btn btn-success-military" form="formModifierPersonnel">
                    <i class="fas fa-save me-1"></i>Enregistrer les modifications
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.nav-tabs .nav-link {
    color: var(--text-light);
    border: none;
    background: transparent;
    margin-right: 10px;
    border-radius: 25px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(212, 175, 55, 0.2);
    color: var(--accent-color);
}

.nav-tabs .nav-link.active {
    background: var(--accent-color);
    color: var(--primary-color);
    font-weight: 600;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid var(--accent-color);
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid var(--accent-color);
}

.timeline-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 5px;
}

.list-group-item {
    border: 1px solid #dee2e6;
    margin-bottom: 5px;
    border-radius: 8px;
}

.badge {
    font-size: 0.75em;
}

/* Timeline verticale pour l'historique */
.timeline-vertical {
    position: relative;
    padding-left: 20px;
}

.timeline-vertical::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item-vertical {
    position: relative;
    margin-bottom: 20px;
    padding-left: 25px;
}

.timeline-marker-vertical {
    position: absolute;
    left: -8px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 1;
}

.timeline-content-vertical {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border-left: 3px solid #dee2e6;
}

@media print {
    .btn, .nav-tabs {
        display: none !important;
    }

    .tab-content .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
}
</style>
{% endblock %}

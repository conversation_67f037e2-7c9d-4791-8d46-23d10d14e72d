#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour supprimer les 25 anciennes tables RH avant migration vers architecture complète
"""

import mysql.connector
from mysql.connector import Error
import sys

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

# Liste des 25 anciennes tables RH à supprimer
ANCIENNES_TABLES_RH = [
    # Tables principales
    'personnel',
    'conjoint', 
    'enfant',
    
    # Tables médicales
    'situation_medicale',
    'vaccination',
    'ptc',
    
    # Tables d'absences
    'permission',
    'desertion',
    'detachement',
    
    # Tables de mouvements
    'mutation_fonction',
    'sejour_ops',
    'liberation',
    'avancement',
    'sanction',
    
    # Tables associatives
    'personnel_langue',
    'historique_grade',
    
    # Tables de référence RH
    'referentiel_genre',
    'referentiel_categorie',
    'referentiel_groupe_sanguin',
    'referentiel_arme',
    'referentiel_specialite',
    'referentiel_unite',
    'referentiel_grade',
    'referentiel_situation_familiale',
    'referentiel_degre_parente',
    'referentiel_langue'
]

def supprimer_tables_rh():
    """Supprimer toutes les anciennes tables RH"""
    print("🗑️ SUPPRESSION DES ANCIENNES TABLES RH")
    print("=" * 60)
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Désactiver les contraintes de clés étrangères temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        print("   ⚠️ Contraintes FK désactivées temporairement")
        
        tables_supprimees = 0
        tables_inexistantes = 0
        
        for table in ANCIENNES_TABLES_RH:
            try:
                # Vérifier si la table existe
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if cursor.fetchone():
                    # Supprimer la table
                    cursor.execute(f"DROP TABLE `{table}`")
                    print(f"   ✅ Table '{table}' supprimée")
                    tables_supprimees += 1
                else:
                    print(f"   ⚠️ Table '{table}' n'existe pas")
                    tables_inexistantes += 1
                    
            except Error as e:
                print(f"   ❌ Erreur lors de la suppression de '{table}': {e}")
        
        # Réactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        print("   ✅ Contraintes FK réactivées")
        
        connection.commit()
        
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DE LA SUPPRESSION:")
        print(f"   Tables supprimées: {tables_supprimees}")
        print(f"   Tables inexistantes: {tables_inexistantes}")
        print(f"   Total traité: {len(ANCIENNES_TABLES_RH)}")
        
        if tables_supprimees > 0:
            print("\n✅ SUPPRESSION RÉUSSIE!")
            print("💡 Prêt pour la création des nouvelles tables")
        else:
            print("\n⚠️ Aucune table supprimée")
            
        return True
        
    except Error as e:
        print(f"\n❌ ERREUR DE CONNEXION: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("   🔌 Connexion fermée")

def verifier_suppression():
    """Vérifier que les tables ont bien été supprimées"""
    print("\n🔍 VÉRIFICATION DE LA SUPPRESSION")
    print("-" * 40)
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Lister toutes les tables restantes
        cursor.execute("SHOW TABLES")
        tables_restantes = [table[0] for table in cursor.fetchall()]
        
        # Vérifier si des anciennes tables RH existent encore
        tables_rh_restantes = [table for table in tables_restantes if table in ANCIENNES_TABLES_RH]
        
        if tables_rh_restantes:
            print(f"   ⚠️ {len(tables_rh_restantes)} tables RH encore présentes:")
            for table in tables_rh_restantes:
                print(f"      - {table}")
            return False
        else:
            print("   ✅ Toutes les anciennes tables RH ont été supprimées")
            print(f"   📊 {len(tables_restantes)} tables restantes dans la base")
            return True
            
    except Error as e:
        print(f"   ❌ Erreur de vérification: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 MIGRATION ARCHITECTURE RH COMPLÈTE - ÉTAPE 1")
    print("Suppression des anciennes tables RH (25 tables)")
    print("=" * 60)
    
    # Demander confirmation
    print("⚠️ ATTENTION: Cette opération va supprimer TOUTES les données RH existantes!")
    print("Les 203 militaires actuels seront perdus.")
    
    confirmation = input("\nÊtes-vous sûr de vouloir continuer? (oui/non): ").lower().strip()
    
    if confirmation not in ['oui', 'o', 'yes', 'y']:
        print("❌ Opération annulée par l'utilisateur")
        sys.exit(0)
    
    # Supprimer les tables
    if supprimer_tables_rh():
        # Vérifier la suppression
        if verifier_suppression():
            print("\n🎉 ÉTAPE 1 TERMINÉE AVEC SUCCÈS!")
            print("💡 Vous pouvez maintenant exécuter l'étape 2: création des nouvelles tables")
        else:
            print("\n⚠️ Suppression incomplète - vérifiez manuellement")
    else:
        print("\n❌ ÉCHEC DE LA SUPPRESSION")
        sys.exit(1)

if __name__ == "__main__":
    main()

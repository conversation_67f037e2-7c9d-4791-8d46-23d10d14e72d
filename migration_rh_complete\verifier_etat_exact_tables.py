#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier l'état exact des tables dans la base de données
"""

import mysql.connector
from mysql.connector import Error

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion-art'
}

# Tables selon architecture_rh_complete.md
TABLES_REFERENTIELS = [
    'referentiel_type_sanction',
    'referentiel_type_mvt', 
    'referentiel_origine',
    'referentiel_lieu_medical',
    'referentiel_etat_medical',
    'referentiel_type_medical',
    'referentiel_code_maladie',
    'referentiel_situation_familiale',
    'referentiel_type_permission',
    'referentiel_type_absence',
    'referentiel_type_fonction',
    'referentiel_type_note',
    'referentiel_categorie_grade'
]

TABLES_DONNEES = [
    'Ville',
    'Personne', 
    'Adresse',
    'Arm',
    'Unite',
    'Sous_unite',
    'referentiel_grade',
    'Militaire',
    'Conjointe',
    'Enfant',
    'Fonction',
    'Notation',
    'Mission',
    'Mouvement',
    'Punition',
    'Jugement',
    'Medical',
    'Permission',
    'Absence',
    'Promotion',
    'Evenement',
    'Permanence',
    'Tache'
]

def verifier_tables_existantes():
    """Vérifier quelles tables existent réellement"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Lister toutes les tables
        cursor.execute("SHOW TABLES")
        tables_db = [table[0] for table in cursor.fetchall()]
        
        print("🔍 VÉRIFICATION EXACTE DES TABLES")
        print("=" * 70)
        print(f"📊 Total tables dans la base: {len(tables_db)}")
        
        # Vérifier les référentiels
        print(f"\n📋 TABLES DE RÉFÉRENCE ({len(TABLES_REFERENTIELS)} attendues):")
        ref_existantes = []
        ref_manquantes = []
        
        for table in TABLES_REFERENTIELS:
            if table in tables_db or table.lower() in [t.lower() for t in tables_db]:
                ref_existantes.append(table)
                print(f"   ✅ {table}")
            else:
                ref_manquantes.append(table)
                print(f"   ❌ {table}")
        
        # Vérifier les tables de données
        print(f"\n📊 TABLES DE DONNÉES ({len(TABLES_DONNEES)} attendues):")
        data_existantes = []
        data_manquantes = []
        
        for table in TABLES_DONNEES:
            # Vérifier avec différentes casses
            table_found = False
            for db_table in tables_db:
                if table.lower() == db_table.lower():
                    data_existantes.append(table)
                    print(f"   ✅ {table} (trouvée comme '{db_table}')")
                    table_found = True
                    break
            
            if not table_found:
                data_manquantes.append(table)
                print(f"   ❌ {table}")
        
        # Résumé
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ:")
        print(f"   Référentiels: {len(ref_existantes)}/{len(TABLES_REFERENTIELS)} présents")
        print(f"   Données: {len(data_existantes)}/{len(TABLES_DONNEES)} présentes")
        print(f"   TOTAL: {len(ref_existantes) + len(data_existantes)}/36 tables")
        
        if ref_manquantes:
            print(f"\n⚠️ RÉFÉRENTIELS MANQUANTS ({len(ref_manquantes)}):")
            for table in ref_manquantes:
                print(f"      - {table}")
        
        if data_manquantes:
            print(f"\n⚠️ TABLES DE DONNÉES MANQUANTES ({len(data_manquantes)}):")
            for table in data_manquantes:
                print(f"      - {table}")
        
        # Tables supplémentaires non prévues
        toutes_attendues = TABLES_REFERENTIELS + TABLES_DONNEES
        tables_supplementaires = []
        for table in tables_db:
            if not any(table.lower() == attendue.lower() for attendue in toutes_attendues):
                # Exclure les tables non-RH connues
                if not table.lower().startswith(('vehicule', 'courrier', 'entretien', 'stagiaire', 'stage', 'promotion')):
                    tables_supplementaires.append(table)
        
        if tables_supplementaires:
            print(f"\n📋 TABLES SUPPLÉMENTAIRES ({len(tables_supplementaires)}):")
            for table in tables_supplementaires:
                print(f"      - {table}")
        
        return len(ref_existantes), len(data_existantes), ref_manquantes, data_manquantes
        
    except Error as e:
        print(f"❌ Erreur: {e}")
        return 0, 0, [], []
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def verifier_donnees_referentiels():
    """Vérifier si les référentiels sont peuplés"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print(f"\n📊 VÉRIFICATION DES DONNÉES DANS LES RÉFÉRENTIELS:")
        
        for table in TABLES_REFERENTIELS:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                status = "📈" if count > 0 else "📭"
                print(f"   {status} {table}: {count} enregistrements")
            except Error:
                print(f"   ❌ {table}: table inexistante ou erreur")
        
        # Vérifier quelques tables de données importantes
        tables_importantes = ['Ville', 'Arm', 'Unite', 'referentiel_grade']
        print(f"\n📊 VÉRIFICATION DES DONNÉES IMPORTANTES:")
        
        for table in tables_importantes:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                status = "📈" if count > 0 else "📭"
                print(f"   {status} {table}: {count} enregistrements")
            except Error:
                print(f"   ❌ {table}: table inexistante ou erreur")
        
    except Error as e:
        print(f"❌ Erreur vérification données: {e}")
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def recommandations(ref_count, data_count, ref_manquantes, data_manquantes):
    """Donner des recommandations basées sur l'état"""
    print(f"\n💡 RECOMMANDATIONS:")
    
    if ref_count == len(TABLES_REFERENTIELS) and data_count == len(TABLES_DONNEES):
        print("   🎉 Toutes les tables sont présentes!")
        print("   ➡️ Vous pouvez passer aux tests des modèles")
    
    elif ref_count == len(TABLES_REFERENTIELS) and data_count < len(TABLES_DONNEES):
        print("   ✅ Tous les référentiels sont présents")
        print(f"   ⚠️ {len(data_manquantes)} tables de données manquantes")
        print("   ➡️ Exécutez le script de création des tables manquantes")
    
    elif ref_count < len(TABLES_REFERENTIELS):
        print(f"   ⚠️ {len(ref_manquantes)} référentiels manquants")
        print("   ➡️ Exécutez d'abord le script de création des référentiels")
    
    else:
        print("   🔧 État mixte - vérifiez les scripts de migration")

def main():
    """Fonction principale"""
    print("🚀 VÉRIFICATION EXACTE DE L'ÉTAT DES TABLES")
    print("Architecture RH complète (36 tables)")
    print("=" * 70)
    
    ref_count, data_count, ref_manquantes, data_manquantes = verifier_tables_existantes()
    verifier_donnees_referentiels()
    recommandations(ref_count, data_count, ref_manquantes, data_manquantes)

if __name__ == "__main__":
    main()
